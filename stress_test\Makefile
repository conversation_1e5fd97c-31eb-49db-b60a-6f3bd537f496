# CC=/usr/local/arm/cross/am335xt3/devkit/bin/arm-arago-linux-gnueabi-gcc
#3568
# CC=aarch64-linux-gnu-gcc
#d9
#CC=/tool/gcc_linaro/gcc-linaro-7.3.1-2018.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc



test_start:main.o config_file.o file.o manage.o pidfile.o net_manage.o
#	$(CC)	-Wall	main.o config_file.o file.o manage.o pidfile.o net_manage.o -o  test_start -lpthread -lrt
	$(CC)	-Wall	main.o config_file.o file.o manage.o pidfile.o net_manage.o -o  test_start -lpthread
main.o:main.c config_file.h file.h pidfile.h manage.h net_manage.h
	$(CC)	-c	-Wall	main.c	-o	main.o -lpthread
config_file.o:config_file.c config_file.h
	$(CC)	-c	-Wall	config_file.c	-o	config_file.o
file.o:file.c file.h
	$(CC)	-c	-Wall	file.c	-o	file.o
manage.o:manage.c manage.h file.h display.h
#	$(CC)	-c	-Wall -Wall -std=gnu99	manage.c	-o	manage.o
	$(CC)	-c	-Wall	manage.c	-o	manage.o
pidfile.o:pidfile.c pidfile.h
	$(CC)	-c	-Wall	pidfile.c	-o	pidfile.o
net_manage.o:net_manage.c net_manage.h
	$(CC)	-c	-Wall	net_manage.c	-o	net_manage.o

clean:
	$(RM) *.o	test_start

