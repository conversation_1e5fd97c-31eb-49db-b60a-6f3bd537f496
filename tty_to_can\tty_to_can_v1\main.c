#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <fcntl.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <sys/time.h>
#include <sys/stat.h>
#include <time.h>
#include "serial.h"
#include "file.h"
#include "display.h"

char ver[20] = {"ver01.001"};

int fd;
struct_tty_param tty_param = {9600, 8, 1, 'N', 0};

// 数据收发应用
unsigned char send_buff[2200], receive_buff[2200], temp_rx_buff[2200];
char new_receive[100];
unsigned char trans_data[1000];
unsigned int send_num = 0, receive_num = 0;

int active_send_mode = 0, active_send_num = 8, active_send_time = 2000, receive_timeout = 0, temp_rx_point = 0;
int real_send_num = 0;
int loopback_send_mode = 0;
unsigned long last_time = 0, present_time = 0;
unsigned long total_send = 0, total_receive = 0;
unsigned long total_send_group = 0, want_send_times = 0;
char count_numb = 0;
int err_count = 0, receive_state = 0;
unsigned int read_point = 0, write_point = 0;

// 存储日志
char history_log_path[10] = {"log"};
char temp_log_path[10] = {"temp"};
char history_log_name[50] = {0};
char temp_log_name[50] = {0};
char log_path[50] = {"/root/"};
FILE *pHistoryLogFile = NULL;
FILE *pTempLogFile = NULL;

char dev[20];

/*
 * @description : 自定义打印函数
 * @param - buff:  打印数据缓冲区
 * @param - lens:  打印数据长度
 * @param - mode:  打印格式
 * @return		: 无
 */
void func_my_print(unsigned char *buff, unsigned int lens, unsigned char mode, unsigned char save)
{
    int i = 0;

    switch (mode)
    {
    case 'H': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "0X%02X  ", buff[i]);
            else
                printf("0X%02X  ", buff[i]);
        }
        break;
    case 'h': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%02x  ", buff[i]);
            else
                printf("%02x  ", buff[i]);
        }
        break;
    case 'd': // 按照10进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%02d  ", buff[i]);
            else
                printf("%02d  ", buff[i]);
        }
        break;
    case 'c': // 按照字符打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%c", buff[i]);
            else
                printf("%c", buff[i]);
        }
        break;
    default:
        break;
    }
    if (!save)
        printf("\n");
    else
        file_write_fmt(pHistoryLogFile, "\n\n");
}
unsigned long func_get_system_time_ms(void)
{
    struct timespec time = {0, 0};

    clock_gettime(CLOCK_MONOTONIC, &time);
    return (time.tv_sec * 1000 + time.tv_nsec / 1000000);
}

/*
 * @description  : 打印参数设置格式
 * @param - pname: 函数名
 * @return		 : 无
 */
static void print_usage(const char *pname)
{
    printf("Usage: %s device [-l databit] [-s] [-o] [-e] [-n] [-m] [-s] [-hw]  [-b baudrate] [-t len time] [-c times] [-L] [-p path] [-v]"
           "\n\t'-l' for 5/6/7/8 data bit"
           "\n\t'-s' for 2 stop bit"
           "\n\t'-o' for PARODD "
           "\n\t'-e' for PARENB"
           "\n\t'-n' for check disable"
           "\n\t'-m' for MARK check "
           "\n\t'-s' for SPACE check"
           "\n\t'-hw' for HW flow control enable"
           "\n\t'-b baudrate' for different baudrate"
           "\n\t'-t len time' for interval set time actively sends the len data, unit is ms,time should not be less than 1"
           "\n\t'-c times'  for total send times"
           "\n\t'-L' for app loopback receive data"
           "\n\t'-p path' for log file path  example : /root/"
           "\n\t'-v' show version"
           "\n\texample :  8n1 921600  80 Bytes was send every 2s total send 10 times  --> ./tty_to_can_type  ttyS4 -b 921600 -t 80 2000 -c 10"
           "\n\texample :  8n1 921600 loopback receive data --> ./tty_to_can_type  ttyS5 -b 921600 -L\n ",
           pname);
}

/*
 * @description      : 解析函数带入参数
 * @param - numb     : 参数个数
 * @param - *param   : 带入参数数组指针
 * @param - *tty     : 串口应用参数
 * @return		     : 无
 */
void get_param(int numb, char *param[], struct_tty_param *tty)
{
    int i = 0, len = 0, j = 0;
    unsigned char data_bit = 0;

    if (numb <= 2)
        return;

    for (i = 2; i < numb; i++)
    {
        if (!strcmp(param[i], "-l"))
        {
            i++;
            data_bit = atoi(param[i]);
            switch (data_bit)
            {
            case 5:
                tty->data_bit = 5;
                break;
            case 6:
                tty->data_bit = 6;
                break;
            case 7:
                tty->data_bit = 7;
                break;
            case 8:
                tty->data_bit = 8;
                break;
            default:
                tty->data_bit = 8;
                break;
            }
            continue;
        }
        if (!strcmp(param[i], "-s"))
        {
            tty->stop_bit = 2;
            continue;
        }
        if (!strcmp(param[i], "-o"))
        {
            tty->check = 'O';
            continue;
        }
        if (!strcmp(param[i], "-e"))
        {
            tty->check = 'E';
            continue;
        }
        if (!strcmp(param[i], "-m"))
        {
            tty->check = 'M';
            continue;
        }
        if (!strcmp(param[i], "-s"))
        {
            tty->check = 'S';
            continue;
        }
        if (!strcmp(param[i], "-n"))
        {
            tty->check = 'N';
            continue;
        }
        if (!strcmp(param[i], "-hw"))
        {
            tty->hardware = 1;
            continue;
        }
        if (!strcmp(param[i], "-b"))
        {
            i++;
            tty->baudrate = atoi(param[i]);
            continue;
        }
        if (!strcmp(param[i], "-t"))
        {
            active_send_mode = 1;

            i++;
            len = atoi(param[i]);
            if (len > 0)
            {
                active_send_num = len;
                for (j = 0; j < active_send_num; j++)
                {
                    send_buff[j] = j;
                    if (!send_buff[j])
                        send_buff[j] = 1;
                }

                i++;
                len = atoi(param[i]); // 发送间隔
                if (len > 1)
                {
                    active_send_time = len; // 转换为ms单位
                    receive_timeout = (int)(active_send_time * 0.8);
                }
                else
                {
                    printf("The sending interval cannot be less than 1.\n");
                    print_usage(param[0]);
                    exit(1);
                }
            }
            continue;
        }
        if (!strcmp(param[i], "-c"))
        {
            i++;
            len = atoi(param[i]);
            if (len > 0)
            {
                want_send_times = len;
            }
            continue;
        }
        if (!strcmp(param[i], "-L"))
        {
            loopback_send_mode = 1;
            continue;
        }
        if (!strcmp(param[i], "-p"))
        {
            i++;
            strcpy(log_path, param[i]);
            continue;
        }
        if (!strcmp(param[i], "-v"))
        {
            printf("tty_to_can ver:  %s\n", ver);
            continue;
        }
    }
}

/*
 * @description    : 信号处理函数
 * @param - iSignNo: 信号
 * @return		   : 执行结果
 */
void SignHandler(int iSignNo)
{
    printf("total_send=%ld, total_receive=%ld \n", total_send, total_receive);
    file_write_time_fmt(pTempLogFile, "%s total_send=%ld, total_receive=%ld \n", dev, total_send, total_receive);
    if (err_count)
    {
        file_write_time_fmt(pTempLogFile, "%s test result is FAILED \n", dev);
        printf(L_RED "%s test result is FAILED" NONE "\n", dev);
    }
    else
    {
        file_write_time_fmt(pTempLogFile, "%s test result is PASSED \n", dev);
        printf(L_GREEN "%s test result is PASSED" NONE "\n", dev);
    }

    fflush(pHistoryLogFile);     /*将数据同步至ROM*/
    file_close(pHistoryLogFile); /*关闭文件*/

    fflush(pTempLogFile);     /*将数据同步至ROM*/
    file_close(pTempLogFile); /*关闭文件*/

    usleep(20000);
    if (fd > 0)
        close(fd);
    exit(1);
}
/*
 * @description  : 主函数
 * @param - argc : 参数个数
 * @param - *argv: 带入参数数组指针
 * @return		 : 执行结果
 */
int main(int argc, char *argv[])
{
    int result = 0;
    char history_log_full_name[100] = {0};
    char temp_log_full_name[100] = {0};
    int i = 0, j = 0;
    unsigned int id = 0, id_flag = 0;
    unsigned char fram_len = 0;
    char data[100];
    int len = 0;
    int real_len = 0;
    unsigned int temp_read_point = 0;

    // 检测是否有参数
    if (argc < 2 || strncmp(argv[1], "tty", 3))
    {
        print_usage(argv[0]);
        exit(1);
    }
    // 检测是否有--h或--help
    if ((!strcmp(argv[1], "--h")) || (!strcmp(argv[1], "--help")))
    {
        print_usage(argv[0]);
        exit(1);
    }

    strcpy(dev, "/dev/");
    strcat(dev, argv[1]);
    // 从main函数带来的参数解析为串口参数
    get_param(argc, argv, &tty_param);

    // 准备历史日志文件
    file_add_full_fath(history_log_full_name, log_path, history_log_path);
    if ((access(history_log_full_name, 0)) == -1)
    {
        printf("not exist\n");
        mkdir(history_log_full_name, S_IRWXU);
    }
    sprintf(history_log_name, "/%s_history.log", argv[1]);
    strcat(history_log_full_name, history_log_name);
    pHistoryLogFile = file_open(history_log_full_name, ATWR);
    if (NULL == pHistoryLogFile)
        exit(1);
    // 准备临时日志文件
    file_add_full_fath(temp_log_full_name, log_path, temp_log_path);
    if ((access(temp_log_full_name, 0)) == -1)
    {
        printf("not exist\n");
        mkdir(temp_log_full_name, S_IRWXU);
    }
    sprintf(temp_log_name, "/%s_temp.log", argv[1]);
    strcat(temp_log_full_name, temp_log_name);
    pTempLogFile = file_open(temp_log_full_name, WTWR);
    if (NULL == pTempLogFile)
        exit(1);

    // 给历史文件和临时文件附测试头
    file_write_head(pHistoryLogFile, tty_param, dev);
    file_write_head(pTempLogFile, tty_param, dev);
    file_write_time_fmt(pHistoryLogFile, "[%s nwrite=%d]  ", dev, active_send_num);
    func_my_print(send_buff, active_send_num, 'h', 1); // 将收到的数据打印出来

    // 进程被kill 需要保存文件
    signal(SIGTERM, SignHandler);
    // 按下 ctrl + c 需要保存文件
    signal(SIGINT, SignHandler);

    // 当知道设备名称时可以直接赋值dev，例strcpy(dev, "/dev/ttymxc1");
    // 打开串口 设置可读写，不被输入影响，不等待外设响应
    fd = open(dev, O_RDWR | O_NOCTTY | O_NDELAY);
    if (fd < 0)
    {
        perror(dev);
        printf("Can't Open Serial Port %s \n", dev);
        exit(0);
    }
    else
    {
        printf("baudrate=%ld,data_bit=%d,stop_bit=%d,check='%c'\n", tty_param.baudrate, tty_param.data_bit, tty_param.stop_bit, tty_param.check);
        // 设置串口参数
        if ((result = func_set_opt(fd, tty_param.baudrate, tty_param.data_bit, tty_param.stop_bit, tty_param.check, tty_param.hardware)) < 0)
        {
            perror("set_opt error");
            exit(0);
        }
    }
    while (1)
    {
        receive_num = func_receive_frame(fd, new_receive, 51); /*读取串口收到的数据*/
        if (receive_num > 0)
        {
            if (1 == loopback_send_mode) // 回环需要存储接收到的原始数据
            {
                file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, receive_num);
                func_my_print(new_receive, receive_num, 'h', 1); // 将收到的数据打印出来
            }

            for (j = 0; j < receive_num; j++)
            {
                receive_buff[write_point++] = new_receive[j];
                write_point = write_point % 2200;
            }
            if (write_point >= read_point)
            {
                real_len = write_point - read_point;
            }
            else
            {
                real_len = write_point + 2200 - read_point;
            }
            // printf("new_receive=%d,r_w_len=%d \n", receive_num, real_len);

            if (real_len >= 17)
            {
                temp_read_point = read_point;

                for (j = 0; j < real_len; j++)
                {
                    trans_data[j] = receive_buff[temp_read_point++];
                    temp_read_point = temp_read_point % 2200;
                }

                // printf("[nread=%d] ", receive_num);
                // func_my_print(receive_buff, receive_num, 'h', 0); /*打印接收到的数据*/
                for (i = 0; i < real_len; i++)
                {
                    if ((trans_data[i] == 0x41) && (trans_data[i + 1] == 0x54))
                    {
                        id = ((trans_data[i + 2] << 24) | (trans_data[i + 3] << 16) | (trans_data[i + 4] << 8) | trans_data[i + 5]);
                        id_flag = (id & 0x04);
                        if (0x04 == id_flag)
                        {
                            id = id >> 3;
                        }
                        else
                        {
                            id = id >> 21;
                        }
                        fram_len = trans_data[i + 6];
                        if ((real_len - i - 7) < (fram_len + 2))
                            break;
                        if (1 == loopback_send_mode) // 回环需要存储接收到的解析数据
                        {
                            len = Func_Dprintf(data, "id = 0x%x data_len = %d data : ", id, fram_len);
                            fwrite(data, sizeof(char), len, pHistoryLogFile);    /*写入数据*/
                            func_my_print(&trans_data[i + 7], fram_len, 'h', 1); // 将收到的数据打印出来
                        }
                        memcpy(&temp_rx_buff[temp_rx_point], &trans_data[i + 7], fram_len);
                        temp_rx_point += fram_len;
                        total_receive += fram_len;
                        read_point += 17;
                        read_point = read_point % 2200;
                        i += 16;
                        // if (id > 0)
                        //{
                        //  frame_count++;
                        //   printf("frame_count=%d\n", frame_count);
                        //}
                    }
                }
            }
        }
        // 组织发送数据
        if ((1 == loopback_send_mode) && (temp_rx_point > 0)) // 数据回环处理
        {
            send_num = temp_rx_point;

            memcpy(send_buff, temp_rx_buff, send_num);
            temp_rx_point = 0;
        }
        else if (1 == active_send_mode)
        {
            present_time = func_get_system_time_ms();

            if (WAIT_RECEIVE == receive_state)
            {
                if (receive_num > 0)
                {
                    if (temp_rx_point >= active_send_num)
                    {
                        // printf("real_use=%ld,time_out=%d\n", (present_time - last_time), receive_timeout);
                        if (0 == strncmp((const char *)temp_rx_buff, (const char *)send_buff, active_send_num))
                        {
                            receive_state = OK_RECEIVE;
                        }
                        else
                        {
                            printf(L_RED "%s data receive and send diffrent.!!!" NONE "\n", dev);
                            file_write_time_fmt(pHistoryLogFile, "%s data receive and send diffrent.!!!\n", dev);
                            // len = Func_Dprintf(data, "%s test times %d,spi data receive and send diffrent.\n", dev, run->times);
                            // fwrite(data, sizeof(char), len, pfile); /*写入数据*/
                            file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, temp_rx_point);
                            func_my_print(temp_rx_buff, temp_rx_point, 'h', 1); // 将收到的数据打印出来
                            err_count++;
                            receive_state = ERR_RECEIVE;
                        }
                    }
                }
                if ((present_time - last_time) >= receive_timeout)
                {
                    printf(L_RED "%s data receive time out.!!!" NONE "\n", dev);
                    file_write_time_fmt(pHistoryLogFile, "%s data receive time out.!!!\n", dev);
                    // len = Func_Dprintf(data, "%s test times %d,spi data receive and send diffrent.\n", dev, run->times);
                    // fwrite(data, sizeof(char), len, pfile); /*写入数据*/
                    if (temp_rx_point > 0)
                    {
                        file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, temp_rx_point);
                        func_my_print(temp_rx_buff, temp_rx_point, 'h', 1); // 将收到的数据打印出来
                    }
                    err_count++;
                    receive_state = ERR_RECEIVE;
                }
            }

            if ((present_time - last_time) >= active_send_time)
            {
                send_num = active_send_num;

                last_time = func_get_system_time_ms();
                receive_state = WAIT_RECEIVE;
                temp_rx_point = 0;
            }
        }

        // 发送数据
        if (send_num > 0)
        {
            if (want_send_times)
            {
                if (total_send_group >= want_send_times)
                {
                    printf("total_send=%ld, total_receive=%ld \n", total_send, total_receive);
                    file_write_time_fmt(pTempLogFile, "%s total_send=%ld, total_receive=%ld \n", dev, total_send, total_receive);
                    if (err_count)
                    {
                        file_write_time_fmt(pTempLogFile, "%s test result is FAILED \n", dev);
                        printf(L_RED "%s test result is FAILED" NONE "\n", dev);
                    }
                    else
                    {
                        file_write_time_fmt(pTempLogFile, "%s test result is PASSED \n", dev);
                        printf(L_GREEN "%s test result is PASSED" NONE "\n", dev);
                    }
                    fflush(pHistoryLogFile);     /*将数据同步至ROM*/
                    file_close(pHistoryLogFile); /*关闭文件*/

                    fflush(pTempLogFile);     /*将数据同步至ROM*/
                    file_close(pTempLogFile); /*关闭文件*/

                    close(fd);
                    exit(0);
                }
            }

            real_send_num = func_send_buff(fd, send_buff, send_num);
            if (real_send_num > 0)
            {
                total_send_group++;
                total_send += real_send_num;
                // file_write_time_fmt(pHistoryLogFile, "[%s nwrite=%d]  ", dev, real_send_num);
                // func_my_print(send_buff, real_send_num, 'h', 1); // 将收到的数据打印出来
            }
            if (0 == active_send_mode)
                memset(send_buff, 0, send_num);
            send_num = 0;
        }
        usleep(30000); // 10ms
    }
    close(fd);
    exit(0);
}
