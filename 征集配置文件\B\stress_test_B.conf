##### stress test command list ###############################
#
# This file describes stress test command and part of parameter.
#
# Empty lines and lines starting with # are ignored
# Note: Please do not modify. 

# test pattern 0:sponsor_in_order  1:sponsor_concurrent  2:response_in_order 3:response_concurrent
# pay attention : must fill in
pattern = 2

# one times whole test time interval,the unit is second.
test_interval = 600 

# temperature limit.The temperature becomes red after exceeding the limit.
temperature_limit = 105 
temperature_coef = 1000

# This parameter works when test pattern is sponsor_in_order.
# This parameter is the number of whole test.-1 is infinite times .0 is invalid,then change to infinite times.
whole_test_circle = -1

#app path
#app_path = /type_test/

info_path = /proc/version
info_key = 

temp_path = /sys/class/thermal/thermal_zone0/temp
temp_key = 

limit0_path = /sys/class/thermal/thermal_zone0/trip_point_0_temp
limit0_key = 

limit1_path = /sys/class/thermal/thermal_zone0/trip_point_1_temp
limit1_key = 

freq_path = /sys/devices/system/cpu/cpu0/cpufreq/scaling_cur_freq
freq_key = 

list_f_path = /sys/devices/system/cpu/cpu0/cpufreq/scaling_available_frequencies
list_f_key = 

hbset = /sys/class/leds/work/trigger
hbconctol = /sys/class/leds/work/brightness

pretreatment_cmd1 = ifconfig eth0 up
pretreatment_cmd2 = ifconfig eth1 up

# pay attention : The same groups of commands is executed simultaneously
# Different groups of commands, executed successively
# circle : -1 is infinite times.0 is unexecuted

[SOC]
circle = 0
order1 = 

[DDR]
circle = 1 
group1order1 = memtester 100M 1
group1timeout1 = 1200
group1order2 = memtester 100M 1
group1timeout2 = 1200
group2order1 = bw_mem
group2timeout1 = 300

#The first parameter is test path ,The second parameter is numb*2M
[EMMC]
circle = 1
group1order1 = dd /  100 60.0 80.0 direct
group1timeout1 = 60
group1param1 = 25 30

[NAND]
circle = 0
group1order1 = dd /dev/mtdblock7 /  100 60.0 80.0
group1timeout1 = 60
group1param1 = 25 30
group2order1 = check_nand /dev/mtd7 1 10 10

[SD]
circle = 1
group1order1 = dd /run/media/mmcblk1p1/ 10 6 35 direct
group1timeout1 = 60
group1param1 = 5 15

[USB]
circle = 0
group1order1 = dd /run/media/sda1/ 10 3.5 15 direct
group1timeout1 = 60
group1param1 = 2 15
#group1order2 = dd /run/media/sdb1/ 10 3.5 15 direct
#group1timeout2 = 60
#group1param2 = 2 15

[TYPE_C]
circle = 0
group1order1 = 

[CAN]
circle = 1
group1order1 = @app_path/item/can_type can0 -b 1000 -s 75 -e -L -p @app_path/ &
group1order2 = @app_path/item/can_type can1 -b 1000 -s 75 -e -L -p @app_path/ &

[RS485]
circle = 1
group1order1 = @app_path/item/tty_type ttyS3 -n -b 115200 -p @app_path/ -L &
group1order2 = @app_path/item/tty_type ttyS4 -n -b 115200 -p @app_path/ -L &
group1order3 = @app_path/item/tty_type ttyS5 -n -b 115200 -p @app_path/ -L &


[RS232]
circle = 0
group1order1 = 

[SPI]
circle = 0
group1order1 = @app_path/item/spi_type spidev2.0 -s 1000 -t 500 1000 -c 60 -p @app_path/ &
group2order1 = @app_path/item/spi_type spidev2.1 -s 1000 -t 500 1000 -c 60 -p @app_path/ &

[IIC]
circle = 0
group1order1 = 

[NET]
circle = 1
group1order1 = iperf3 /sys/devices/platform/fe2a0000.ethernet/net/eth0
group1order2 = iperf3 /sys/devices/platform/usbdrd/fcc00000.dwc3/xhci-hcd.0.auto/usb6/6-1/6-1:1.0/net/eth2 usb3

[AUDIO]
circle = 0
group1order1 = 

[CAMERA]
circle = 0
group1order1 = 

[SATA]
circle = 0
group1order1 = 

[RTC]
circle = 1
group1order1 = rtc_test /dev/rtc0
group1timeout1 = 300

[G4]
circle = 0
group1order1 = 

[WIFI]
circle = 0
group1order1 = 

[DIDO]
circle = 0
group1order1 = 

[PCIE]
circle = 0
group1order1 = 

[BT]
circle = 0
group1order1 = 

[DISPLAY]
circle = 0
group1order1 = 

