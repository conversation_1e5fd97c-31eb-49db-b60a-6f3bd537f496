# CC=/usr/local/arm/cross/am335xt3/devkit/bin/arm-arago-linux-gnueabi-gcc
#3568
#CC=aarch64-linux-gnu-gcc 


tty_to_can_type:main.o serial.o file.o 
	$(CC)	-Wall	main.o serial.o	file.o -o	tty_to_can_type
main.o:main.c serial.h  file.h display.h
	$(CC)	-c	-Wall	main.c	-o	main.o
serial.o:serial.c
	$(CC)	-c	-Wall	serial.c	-o	serial.o
file.o:file.c file.h
	$(CC)	-c	-Wall	file.c	-o	file.o
clean:
	$(RM) *.o	tty_to_can_type

