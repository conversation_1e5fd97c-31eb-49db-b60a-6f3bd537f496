test name : spi_test
test ver : sv03.001
first edition compilation time :2023/06/15

note:

1)different platforms have to choose different cross-compilation .you need to modify makefile redefine "CC".
2)function describe :MOSI and MISO short connect, regularly send the specified data, and receive at the same time.
Check whether the received data is consistent with the sent data. Inconsistent will be red letter error reminder, consistent do not print information. 
After all test times, the error-free green letter is successful, otherwise the red letter fails.
3)about mode sampling data location
  cpol = 0 cpha = 0         cpol = 0 chap = 1       cpol = 1 cpha = 0         cpol = 1 chap = 1    

      |____                         ____|              ____|     _____             ____      |____
      |     |                        |     |                    |     |                         |     |
  ___|     |___               ___|     |___                |____|                        |____|
      |                                   |                    |                               |
4）--help to print usage.

some more examples:
