#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <signal.h>
#include <sys/stat.h>
#include <time.h>
#include <signal.h>
#include "serial.h"
#include "display.h"
#include "file.h"

char ver[20] = {"ver03.001"};

int tty_fd;
struct_tty_param tty_param = {1000000, 8, 1, 'N', 0};

// 数据收发应用
unsigned char *send_buff, *receive_buff, *temp_rx_buff;
// unsigned char send_buff[2200], receive_buff[2200], temp_rx_buff[2200];
unsigned int send_num = 0, receive_num = 0;

int active_send_mode = 0, active_send_num = 8, active_send_time = 2000, receive_timeout = 0, temp_rx_point = 0;
int real_send_num = 0;
int loopback_send_mode = 0;
unsigned long last_time = 0, present_time = 0;
unsigned long total_send = 0, total_receive = 0;
unsigned long total_send_group = 0, want_send_times = 0;
char count_numb = 0;
int err_count = 0, receive_state = 0;

// 存储日志
char history_log_path[10] = {"log"};
char temp_log_path[10] = {"temp"};
char history_log_name[50] = {0};
char temp_log_name[50] = {0};
char log_path[50] = {"/root/"};
FILE *pHistoryLogFile = NULL;
FILE *pTempLogFile = NULL;

char dev[20];

/*
 * @description : 自定义打印函数
 * @param - buff:  打印数据缓冲区
 * @param - lens:  打印数据长度
 * @param - mode:  打印格式
 * @return		: 无
 */
void func_my_print(unsigned char *buff, unsigned int lens, unsigned char mode, unsigned char save)
{
    int i = 0;

    switch (mode)
    {
    case 'H': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "0X%02X  ", buff[i]);
            else
                printf("0X%02X  ", buff[i]);
        }
        break;
    case 'h': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%02x  ", buff[i]);
            else
                printf("%02x  ", buff[i]);
        }
        break;
    case 'd': // 按照10进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%02d  ", buff[i]);
            else
                printf("%02d  ", buff[i]);
        }
        break;
    case 'c': // 按照字符打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%c", buff[i]);
            else
                printf("%c", buff[i]);
        }
        break;
    default:
        break;
    }
    if (!save)
        printf("\n");
    else
        file_write_fmt(pHistoryLogFile, "\n\n");
}
unsigned long func_get_system_time_ms(void)
{
    struct timespec time = {0, 0};

    clock_gettime(CLOCK_MONOTONIC, &time);
    return (time.tv_sec * 1000 + time.tv_nsec / 1000000);
}

/*
 * @description  : 打印参数设置格式
 * @param - pname: 函数名
 * @return		 : 无
 */
static void print_usage(const char *pname)
{
    printf("Usage: %s device [-l databit] [-s] [-o] [-e] [-n] [-m] [-s] [-hw] [-b baudrate] [-t len time] [-c times] [-L] [-p path] [-v]"
           "\n\t'-l' for 5/6/7/8 data bit"
           "\n\t'-s' for 2 stop bit"
           "\n\t'-o' for PARODD "
           "\n\t'-e' for PARENB"
           "\n\t'-n' for check disable"
           "\n\t'-m' for MARK check "
           "\n\t'-s' for SPACE check"
           "\n\t'-hw' for HW flow control enable"
           "\n\t'-b baudrate' for different baudrate"
           "\n\t'-t len time'  for interval set time actively sends the len data, unit is ms,time should not be less than 1"
           "\n\t'-c times'  for total send times"
           "\n\t'-L' data loopback,send the received data"
           "\n\t'-p path' for log file path  example : /root/"
           "\n\t'-v' show version"
           "\n\texample :  8o1 115200  --> ./tty_demo ttymxc6 -o -b 115200 -t 100 1000 -c 10"
           "\n\texample :  8o1 115200 data loopback  --> ./tty_demo ttymxc6 -o -b 115200 -L\n ",
           pname);
}

/*
 * @description      : 解析函数带入参数
 * @param - numb     : 参数个数
 * @param - *param   : 带入参数数组指针
 * @param - *tty     : 串口应用参数
 * @return		     : 无
 */
void get_param(int numb, char *param[], struct_tty_param *tty)
{
    int i = 0, len = 0, j = 0;
    unsigned char data_bit = 0;

    if (numb <= 2)
        return;

    for (i = 2; i < numb; i++)
    {
        if (!strcmp(param[i], "-l"))
        {
            i++;
            data_bit = atoi(param[i]);
            switch (data_bit)
            {
            case 5:
                tty->data_bit = 5;
                break;
            case 6:
                tty->data_bit = 6;
                break;
            case 7:
                tty->data_bit = 7;
                break;
            case 8:
                tty->data_bit = 8;
                break;
            default:
                tty->data_bit = 8;
                break;
            }
            continue;
        }
        if (!strcmp(param[i], "-s"))
        {
            tty->stop_bit = 2;
            continue;
        }
        if (!strcmp(param[i], "-o"))
        {
            tty->check = 'O';
            continue;
        }
        if (!strcmp(param[i], "-e"))
        {
            tty->check = 'E';
            continue;
        }
        if (!strcmp(param[i], "-m"))
        {
            tty->check = 'M';
            continue;
        }
        if (!strcmp(param[i], "-s"))
        {
            tty->check = 'S';
            continue;
        }
        if (!strcmp(param[i], "-n"))
        {
            tty->check = 'N';
            continue;
        }
        if (!strcmp(param[i], "-hw"))
        {
            tty->hardware = 1;
            continue;
        }
        if (!strcmp(param[i], "-b"))
        {
            i++;
            tty->baudrate = atoi(param[i]);
            continue;
        }
        if (!strcmp(param[i], "-t"))
        {
            active_send_mode = 1;

            i++;
            len = atoi(param[i]);
            if (len > 7)
            {
                active_send_num = len;
                for (j = 0; j < active_send_num; j++)
                {
                    send_buff[j] = j;
                    if (!send_buff[j])
                        send_buff[j] = 1;
                }
                memcpy(&send_buff[(active_send_num - sizeof("SEND_OK") + 1)], "SEND_OK", (sizeof("SEND_OK") - 1));

                i++;
                len = atoi(param[i]); // 发送间隔
                if (len > 1)
                {
                    // active_send_time = len * 1000; //转换为ms单位
                    active_send_time = len; // 转换为ms单位
                    receive_timeout = (int)(active_send_time * 0.8);
                }
                else
                {
                    printf("The sending interval cannot be less than 1.\n");
                    print_usage(param[0]);
                    exit(1);
                }
            }
            else
            {
                printf("The sending data len cannot be less than 7.\n");
                print_usage(param[0]);
                exit(1);
            }
            continue;
        }
        if (!strcmp(param[i], "-c"))
        {
            i++;
            len = atoi(param[i]);
            if (len > 0)
            {
                want_send_times = len;
            }
            continue;
        }
        if (!strcmp(param[i], "-L"))
        {
            loopback_send_mode = 1;
            continue;
        }
        if (!strcmp(param[i], "-p"))
        {
            i++;
            strcpy(log_path, param[i]);
            continue;
        }
        if (!strcmp(param[i], "-v"))
        {
            printf("tty_demo ver:  %s\n", ver);
            continue;
        }
    }
}

/*
 * @description    : 信号处理函数
 * @param - iSignNo: 信号
 * @return		   : 执行结果
 */
void SignHandler(int iSignNo)
{
    int t = 50;

    while (t > 1)
    {
        receive_num = func_receive_frame(tty_fd, receive_buff, BUFF_SIZE);
        usleep(20);
        t--;
    }
    printf("total_send=%ld, total_receive=%ld \n", total_send, total_receive);
    file_write_time_fmt(pTempLogFile, "%s total_send=%ld, total_receive=%ld \n", dev, total_send, total_receive);
    if (err_count)
    {
        file_write_time_fmt(pTempLogFile, "%s test result is FAILED \n", dev);
        printf(L_RED "%s test result is FAILED" NONE "\n", dev);
    }
    else
    {
        file_write_time_fmt(pTempLogFile, "%s test result is PASSED \n", dev);
        printf(L_GREEN "%s test result is PASSED" NONE "\n", dev);
    }

    fflush(pHistoryLogFile);     /*将数据同步至ROM*/
    file_close(pHistoryLogFile); /*关闭文件*/

    fflush(pTempLogFile);     /*将数据同步至ROM*/
    file_close(pTempLogFile); /*关闭文件*/

    usleep(20000);
    close(tty_fd);
    exit(1);
}

/*
 * @description  : 主函数
 * @param - argc : 参数个数
 * @param - *argv: 带入参数数组指针
 * @return		 : 执行结果
 */
int main(int argc, char *argv[])
{
    int result = 0;
    char history_log_full_name[100] = {0};
    char temp_log_full_name[100] = {0};
    int i = 0;

    // 检测是否有参数
    if (argc < 2 || strncmp(argv[1], "tty", 3))
    {
        print_usage(argv[0]);
        exit(1);
    }

    // 检测是否有--h或--help
    if ((!strcmp(argv[1], "--h")) || (!strcmp(argv[1], "--help")))
    {
        print_usage(argv[0]);
        exit(1);
    }

    // 申请缓存
    send_buff = malloc(BUFF_SIZE);
    memset(send_buff, 0, BUFF_SIZE);

    receive_buff = malloc(BUFF_SIZE);
    memset(receive_buff, 0, BUFF_SIZE);

    temp_rx_buff = malloc(BUFF_SIZE);
    memset(temp_rx_buff, 0, BUFF_SIZE);

    strcpy(dev, "/dev/");
    strcat(dev, argv[1]);

    // 从main函数带来的参数解析为CAN口参数
    get_param(argc, argv, &tty_param);

    // 准备历史日志文件
    file_add_full_fath(history_log_full_name, log_path, history_log_path);
    if ((access(history_log_full_name, 0)) == -1)
    {
        printf("not exist\n");
        mkdir(history_log_full_name, S_IRWXU);
    }
    sprintf(history_log_name, "/%s_history.log", argv[1]);
    strcat(history_log_full_name, history_log_name);
    pHistoryLogFile = file_open(history_log_full_name, ATWR);
    if (NULL == pHistoryLogFile)
        exit(1);
    // 准备临时日志文件
    file_add_full_fath(temp_log_full_name, log_path, temp_log_path);
    if ((access(temp_log_full_name, 0)) == -1)
    {
        printf("not exist\n");
        mkdir(temp_log_full_name, S_IRWXU);
    }
    sprintf(temp_log_name, "/%s_temp.log", argv[1]);
    strcat(temp_log_full_name, temp_log_name);
    pTempLogFile = file_open(temp_log_full_name, WTWR);
    if (NULL == pTempLogFile)
        exit(1);

    // 给历史文件和临时文件附测试头
    file_write_head(pHistoryLogFile, tty_param, dev);
    file_write_head(pTempLogFile, tty_param, dev);
    file_write_time_fmt(pHistoryLogFile, "[%s nwrite=%d]  ", dev, active_send_num);
    func_my_print(send_buff, active_send_num, 'h', 1); // 将收到的数据打印出来

    // 进程被kill 需要保存文件
    signal(SIGTERM, SignHandler);
    // 按下 ctrl + c 需要保存文件
    signal(SIGINT, SignHandler);

    tty_fd = open(dev, O_RDWR | O_NOCTTY | O_NDELAY);
    if (tty_fd < 0)
    {
        perror(dev);
        printf("Can't Open Serial Port %s \n", dev);
        exit(0);
    }
    else
    {
        printf("baudrate=%ld,data_bit=%d,stop_bit=%d,check='%c'\n", tty_param.baudrate, tty_param.data_bit, tty_param.stop_bit, tty_param.check);
        // 设置串口参数
        if ((result = func_set_opt(tty_fd, tty_param.baudrate, tty_param.data_bit, tty_param.stop_bit, tty_param.check, tty_param.hardware)) < 0)
        {
            perror("set_opt error");
            exit(0);
        }
    }

    while (1)
    {
        // 接收数据
        receive_num = func_receive_frame(tty_fd, receive_buff, BUFF_SIZE);
        if (receive_num > 0)
        {
            total_receive += receive_num;
            for (i = 0; i < receive_num; i++)
            {
                if (!receive_buff[i])
                    receive_buff[i] = 1;
            }
            // file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, receive_num);
            // func_my_print(receive_buff, receive_num, 'h', 1); // 将收到的数据打印出来
        }
        // 组织发送数据
        if ((1 == loopback_send_mode) && (receive_num > 0)) // 数据回环处理
        {
            file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, receive_num);
            func_my_print(receive_buff, receive_num, 'h', 1); // 将收到的数据打印出来
            memcpy(&temp_rx_buff[temp_rx_point], receive_buff, receive_num);
            temp_rx_point += receive_num;
            temp_rx_point %= 2200;
            if (strstr((const char *)temp_rx_buff, "SEND_OK") != NULL)
            {
                send_num = temp_rx_point;
                memcpy(send_buff, temp_rx_buff, temp_rx_point);
                memset(temp_rx_buff, 0, temp_rx_point);
                temp_rx_point = 0;
            }
        }
        else if (1 == active_send_mode)
        {
            present_time = func_get_system_time_ms();

            if (WAIT_RECEIVE == receive_state)
            {
                if (receive_num > 0)
                {
                    memcpy(&temp_rx_buff[temp_rx_point], receive_buff, receive_num);
                }
                temp_rx_point += receive_num;
                temp_rx_point %= 2200;
                if (temp_rx_point >= active_send_num)
                {
                    if (0 == strncmp((const char *)temp_rx_buff, (const char *)send_buff, active_send_num))
                    {
                        receive_state = OK_RECEIVE;
                    }
                    else
                    {
                        printf(L_RED "%s data receive and send diffrent.!!!" NONE "\n", dev);
                        file_write_time_fmt(pHistoryLogFile, "%s data receive and send diffrent.!!!\n", dev);
                        // len = Func_Dprintf(data, "%s test times %d,spi data receive and send diffrent.\n", dev, run->times);
                        // fwrite(data, sizeof(char), len, pfile); /*写入数据*/
                        file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, temp_rx_point);
                        func_my_print(temp_rx_buff, temp_rx_point, 'h', 1); // 将收到的数据打印出来
                        err_count++;
                        receive_state = ERR_RECEIVE;
                    }
                }
                if ((present_time - last_time) >= receive_timeout)
                {
                    printf(L_RED "%s data receive time out.!!!" NONE "\n", dev);
                    file_write_time_fmt(pHistoryLogFile, "%s data receive time out.!!!\n", dev);
                    // len = Func_Dprintf(data, "%s test times %d,spi data receive and send diffrent.\n", dev, run->times);
                    // fwrite(data, sizeof(char), len, pfile); /*写入数据*/
                    if (temp_rx_point > 0)
                    {
                        file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, temp_rx_point);
                        func_my_print(temp_rx_buff, temp_rx_point, 'h', 1); // 将收到的数据打印出来
                    }
                    err_count++;
                    receive_state = ERR_RECEIVE;
                }
            }

            if ((present_time - last_time) >= active_send_time)
            {
                send_num = active_send_num;

                last_time = func_get_system_time_ms();
                receive_state = WAIT_RECEIVE;
                temp_rx_point = 0;
            }
        }

        // 发送数据
        if (send_num > 0)
        {
            if (want_send_times)
            {
                if (total_send_group >= want_send_times)
                {
                    printf("total_send=%ld, total_receive=%ld \n", total_send, total_receive);
                    file_write_time_fmt(pTempLogFile, "%s total_send=%ld, total_receive=%ld \n", dev, total_send, total_receive);
                    if (err_count)
                    {
                        file_write_time_fmt(pTempLogFile, "%s test result is FAILED \n", dev);
                        printf(L_RED "%s test result is FAILED" NONE "\n", dev);
                    }
                    else
                    {
                        file_write_time_fmt(pTempLogFile, "%s test result is PASSED \n", dev);
                        printf(L_GREEN "%s test result is PASSED" NONE "\n", dev);
                    }
                    fflush(pHistoryLogFile);     /*将数据同步至ROM*/
                    file_close(pHistoryLogFile); /*关闭文件*/

                    fflush(pTempLogFile);     /*将数据同步至ROM*/
                    file_close(pTempLogFile); /*关闭文件*/

                    close(tty_fd);
                    exit(0);
                }
            }

            real_send_num = func_send_frame(tty_fd, send_buff, send_num);
            if (real_send_num > 0)
            {
                total_send += real_send_num;
                total_send_group++;
                // file_write_time_fmt(pHistoryLogFile, "[%s nwrite=%d]  ", dev, real_send_num);
                // func_my_print(send_buff, real_send_num, 'h', 1); // 将收到的数据打印出来
            }
            if (0 == active_send_mode)
                memset(send_buff, 0, send_num);
            send_num = 0;
        }
        usleep(50000); // 50ms
    }
    close(tty_fd);
    exit(0);
}
