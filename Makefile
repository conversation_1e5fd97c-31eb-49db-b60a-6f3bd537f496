MAKE = make

exclude_dirs := can/can_performance i2c/i2c_test spi/spi_test stress_test tty/tty_performance wd/wd_test

# dirs := $($(exclude_dirs))
SUBDIRS := $(exclude_dirs)

all:
	for dir in $(SUBDIRS); do \
		$(MAKE) -C $$dir ;\
		if [ "$$?" != "0" ]; then\
			echo "compile $$dir fail"; \
			exit 1 ; \
		fi;\
	done

#.PHONY: clean distclean cleanall
#$(SUBDIRS):ECHO
#	+$(MAKE) -C $@
ECHO:
	@echo begin compile $(SUBDIRS)

clean: $(clean_dirs) 
	for dir in $(SUBDIRS);\
	do $(MAKE) -C $$dir clean;\
	done
	
cleanall:
	for dir in $(SUBDIRS);\
	do $(MAKE) -C $$dir cleanall;\
	done
