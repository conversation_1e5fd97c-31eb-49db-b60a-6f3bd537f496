#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <signal.h>
#include <linux/types.h>
#include <linux/watchdog.h>
#include <sys/time.h>
#include <time.h>
#include <sys/stat.h>
#include "file.h"

char ver[20] = {"ver02.001"}; // 测试程序版本
char dev[20];                 // 看门狗设备名称
char masege[50] = {"message"};

int fd;
int wfd = -1;

int wd_time = 3, wd_time_ms = 6;
char wd_enable = 1;

// 日志文件
FILE *pLogFile = NULL;
char log_file[30] = {"test.log"};

struct timeval g_PresentTime, g_LastTime; // 记录时间

/*
 * @description  : 打印参数设置格式
 * @param - pname: 函数名
 * @return		 : 无
 */
static void print_usage(const char *pname)
{
    printf("Usage: %s device [-e time] [-d] [-M masege] [-LN logname] [-v]"
           "\n\t'-e time' for enable watchdog ,keep time s do not weed dog system will reboot"
           "\n\t'-d' for watchdog disable"
           "\n\t'-M masege' for record masege to log"
           "\n\t'-LN logname' for rename log name default:test.log  set -LN ID12345  log name is ID12345.log"
           "\n\t'-v' show version"
           "\n\texample :  --> ./wd_test watchdog -e 5 -M 6ull\n ",
           pname);
}

/*
 * @description   : 解析函数带入参数
 * @param - numb  : 参数个数
 * @param - *param: 带入参数数组指针
 * @param - *set  : i2c测试参数
 * @return		  : 无
 */
void get_param(int numb, char *param[])
{
    int i = 0;
    int temp = 0;

    if (numb <= 2)
        return;

    for (i = 2; i < numb; i++)
    {
        if (!strcmp(param[i], "-e"))
        {
            wd_enable = 1;
            i++;
            temp = atoi(param[i]);
            if (temp > 1)
            {
                wd_time = temp;
                wd_time_ms = wd_time + 3; // 超过设置值3s仍未有复位，判定为失败
                wd_time_ms *= 1000;
            }
            continue;
        }
        if (!strcmp(param[i], "-d"))
        {
            wd_enable = 0;
            continue;
        }
        if (!strcmp(param[i], "-M"))
        {
            i++;
            strcpy(masege, param[i]);
            continue;
        }
        if (!strcmp(param[i], "-LN"))
        {
            i++;
            strcpy(log_file, param[i]);
            strcat(log_file, ".log");
            continue;
        }
        if (!strcmp(param[i], "-v"))
        {
            printf("tty_demo ver:  %s\n", ver);
            continue;
        }
    }
}

/*
 * @description    : 信号处理函数
 * @param - iSignNo: 信号
 * @return		   : 执行结果
 */
void SignHandler(int iSignNo)
{
    fflush(pLogFile);     /*将数据同步至ROM*/
    file_close(pLogFile); /*关闭文件*/
    usleep(20000);

    exit(1);
}

/*
 * @description  : 主函数
 * @param - argc : 参数个数
 * @param - *argv: 带入参数数组指针
 * @return		 : 执行结果
 */
int main(int argc, char *argv[])
{
    int flags;
    int dummy;
    char data[100] = {0};
    int len = 0;
    long interval = 0, log_data_len = 0;
    char full_log_file[40] = {0};

    // 检测是否有参数
    if (argc < 2 || strncmp(argv[1], "watchdog", 8))
    {
        print_usage(argv[0]);
        exit(1);
    }

    // 检测是否有--h或--help
    if ((!strcmp(argv[1], "--h")) || (!strcmp(argv[1], "--help")))
    {
        print_usage(argv[0]);
        exit(1);
    }

    strcpy(dev, "/dev/");
    strcat(dev, argv[1]);

    // 从main函数带入的参数
    get_param(argc, argv);

    if ((access("result/", 0)) != -1)
    {
        printf("Dir exists\n");
    }
    else
    {
        printf("not exist\n");
        mkdir("result/", S_IRWXU);
    }

    // 退出前保存日志文件
    strcpy(full_log_file, "result/");
    strcat(full_log_file, log_file);
    pLogFile = file_open(full_log_file, ATWR);
    if (NULL == pLogFile)
        exit(0);
    wfd = fileno(pLogFile);
    if (-1 == wfd)
        exit(0);
    // 进程被kill 需要保存文件
    signal(SIGTERM, SignHandler);
    // 按下 ctrl + c 需要保存文件
    signal(SIGINT, SignHandler);

    // 将测试头存入文件
    file_write_head(pLogFile, "watchdog function and time test", masege, dev, ver);
    fflush(pLogFile); /*将数据同步至ROM*/

    // 打开看门狗外设
    fd = open(dev, O_WRONLY);
    if (fd < 0)
    {
        perror(dev);
        printf("Can't Open watchdog dev %s \n", dev);
        exit(0);
    }
    else
    {
        // 设置看门狗参数
        if (1 == wd_enable)
        {
            len = Func_Dprintf(data, "Watchdog enabled and set time is %d s.\n\n", wd_time);
            file_write(pLogFile, data, len, &log_data_len);
            fflush(pLogFile); /*将数据同步至ROM*/

            flags = WDIOS_ENABLECARD;
            ioctl(fd, WDIOC_SETOPTIONS, &flags);
            printf("Watchdog enabled.\n");
            ioctl(fd, WDIOC_SETTIMEOUT, &wd_time);
        }
        else
        {
            flags = WDIOS_DISABLECARD;
            ioctl(fd, WDIOC_SETOPTIONS, &flags);
            printf("Watchdog card disabled.\n");
        }
    }
    ioctl(fd, WDIOC_KEEPALIVE, &dummy);
    gettimeofday(&g_LastTime, NULL);

    while (1)
    {
        gettimeofday(&g_PresentTime, NULL);
        interval = (g_PresentTime.tv_sec - g_LastTime.tv_sec) * 1000000 + g_PresentTime.tv_usec - g_LastTime.tv_usec;
        interval = interval / 1000;
        if (interval > wd_time_ms)
        {
            printf("watch dog function err!!!\n");
            printf("\e[1;31m"
                   "watch dog test result is FAILED"
                   "\e[0m"
                   "\n");
            len = Func_Dprintf(data, "\rwatch dog function err!!!\n");
            file_write(pLogFile, data, len, &log_data_len);
            len = 0;
            len = Func_Dprintf(data, "watch dog test result is FAILED\n");
            file_write(pLogFile, data, len, &log_data_len);

            fflush(pLogFile); /*将数据同步至ROM*/
            fsync(wfd);
            exit(1);
        }
        printf("real reboot time is %ld ms.\n", interval);
        len = Func_Dprintf(data, "\rreal reboot time is %ld ms.", (interval + 100));
        file_write(pLogFile, data, len, &log_data_len);
        fflush(pLogFile); /*将数据同步至ROM*/
        fsync(wfd);

        usleep(100000);
    }

    exit(0);
}
