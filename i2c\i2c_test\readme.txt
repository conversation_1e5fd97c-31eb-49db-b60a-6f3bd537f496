test name : iic_test
test ver : sv03.001
first edition compilation time :2022/01/29

note:

1)different platforms have to choose different cross-compilation .you need to modify makefile redefine "CC".
2)function describe : The EEP tooling with I2C is required.
The device regularly sends the specified data to the EEP and then reads it back from the EEP. 
Check if the read data agrees with the written data. 
Inconsistent will be red letter error reminder, consistent do not print information. 
After all test times are completed, no error green word will report success, otherwise failure.
3)

some more examples:
