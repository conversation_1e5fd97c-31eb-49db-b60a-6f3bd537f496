# CC=/usr/local/arm/cross/am335xt3/devkit/bin/arm-arago-linux-gnueabi-gcc
#A40i
# CC=/usr/opt/arm/opt/ext-toolchain/bin/arm-linux-gnueabihf-gcc

spi_type:main.o spi.o file.o
	$(CC)	-Wall	main.o spi.o file.o	-o	spi_type
main.o:main.c spi.h file.h display.h
	$(CC)	-c	-Wall	main.c	-o	main.o
spi.o:spi.c
	$(CC)	-c	-Wall	spi.c	-o	spi.o
file.o:file.c file.h
	$(CC)	-c	-Wall	file.c	-o	file.o
clean:
	$(RM) *.o	spi_type

