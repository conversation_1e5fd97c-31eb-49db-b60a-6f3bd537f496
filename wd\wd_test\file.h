
#ifndef FILE_H
#define FILE_H

#define MAX_SYN_LEN 500

typedef enum
{
    ATWR = 0x0,    // 用于存日志文件，在后面追加
    WTWR = 0x01,   // 用于存储接收的文件，若有原文件清0
    ONLY_R = 0x02, // 用于读取要发送的文件

} enum_file_mode;

int Func_Dprintf(char *destpoint, char *fmt, ...);
void file_add_full_fath(char *dest, char *src, char *name);
FILE *file_open(char *path, enum_file_mode mode);
void file_write(FILE *pfile, char *scr_ata, int len, long *syn_len);
void file_write_head(FILE *pfile, char *test_name, char *mesage, char *wd_dev, char *test_ver);
int file_read(FILE *pfile, char *scr_ata, int len, long location);
void file_close(FILE *pfile);

#endif