#ifndef MYCAN_H
#define MYCAN_H

#include <linux/can.h>

#define CAN_MODE 0
#define CAN_FD_MODE 1

#define WAIT_RECEIVE 1
#define OK_RECEIVE 2
#define ERR_RECEIVE 3

extern unsigned long total_send, total_receive;

/*CAN口参数结构体 */
typedef struct
{
    unsigned long baudrate;      /*波特率 5k~1000k*/
    unsigned int id;             /*设备ID*/
    struct can_filter filter;    /*接收设备过滤ID*/
    unsigned char extend;        /*扩展ID*/
    unsigned char loopback_mode; /*回环模式*/

    unsigned char canfd_mode;    /*CANFD模式*/
    unsigned long data_baudrate; /*CANFD模式下需要单独设置数据波特率*/
    float sample_point;
    float data_sample_point;
} struct_can_param;

int func_open_can(char *device, struct_can_param para);
int func_set_can(int fd, struct_can_param para);
int func_receive_can_buff(int fd, unsigned char *buff, int len);
int func_send_can_buff(int fd, unsigned char *buff, int len, struct_can_param param);

int func_open_canfd(char *device, struct_can_param para);
int func_set_canfd(int fd, struct_can_param para);
int func_receive_canfd_buff(int fd, unsigned char *buff, int len);
int func_send_canfd_buff(int fd, unsigned char *buff, int len, struct_can_param param);
// void func_my_print(unsigned char *buff, unsigned int lens, unsigned char mode);
#endif