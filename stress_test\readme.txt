test name : test_start
test ver : sv03.002
first edition compilation time :2022/06/14


note:

1)different platforms have to choose different cross-compilation .you need to modify makefile redefine "CC".
2)function describe : Management test entries executed as required.
Stores all of the test results.
Timed print temperature and frequency.

test ver : sv03.002
time :2022/06/14
2)function describe : add Timed to print the CPU occupancy rate.
Store temperature, frequency, CPU occupancy in the table.

some more examples:

v03.001
2024年9月20 修改查找网络名称
2025年5月6 
1.修改dd测试获取设备名称
2.修改自检打印
3.修改自检完成后发送开始测试信号
4.修改轮次计数
25年5月7日 修改版本号命名为3段，v03.01.01 第一段是大版本升级，第二段是使用过程中问题修改，第三段是兼容适配期间平台差异改动
25年9月2日 修改RTC测试出现打开RTC设备失败问题，修改测试组从10升到30