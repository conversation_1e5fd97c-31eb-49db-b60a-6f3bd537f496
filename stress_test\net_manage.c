#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <pthread.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <netinet/tcp.h>
#include <sys/socket.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <sys/epoll.h>
#include <poll.h>
#include "net_manage.h"
#include "manage.h"
#include "display.h"

int sockfd = 0;
int listenfd = 0;
int resend_len = 0;
char resend_buff[512] = {0};
/*
 * @description  : 检测TCP连接状态
 * @param - fd   : 文件描述符
 * @return		 : 执行结果 链接断返回-1 正常返回0
 */
int func_detect_tcp_link(int fd)
{
    int info_len = 0, re_err = 0;
    struct tcp_info info;

    info_len = sizeof(struct tcp_info);

    getsockopt(fd, IPPROTO_TCP, TCP_INFO, (void *)&info, (socklen_t *)&info_len);
    if (info.tcpi_state != TCP_ESTABLISHED)
    {
        re_err = -1;
    }

    return re_err;
}

/*
 * @description  : TCP客户端与服务端建立链接
 * @param - *fd  : 链接文件描述符
 * @param - *ip  : 服务端ip，
 * @param - port : 服务端端口，
 * @return		 : 执行结果 链接成功返回0 连接失败返回-1
 */
int func_create_tcp_client_link(int *fd, char *ip, unsigned int port)
{
    int value = 0, Flags = 0;
    struct sockaddr_in servaddr;
    fd_set set;
    struct timeval timeout;

    // 设置建立连接等待时间
    timeout.tv_sec = 30;
    timeout.tv_usec = 0;

    /*(1) 创建套接字*/
    if ((*fd = socket(AF_INET, SOCK_STREAM, 0)) == -1)
    {
        value = -1;
        return value;
    }

    /*(2) 设置链接服务器地址结构*/
    bzero(&servaddr, sizeof(servaddr));
    servaddr.sin_family = AF_INET;
    servaddr.sin_addr.s_addr = inet_addr(ip);
    servaddr.sin_port = htons(port);

    // 设置为非阻塞
    Flags = fcntl(*fd, F_GETFL, 0);
    fcntl(*fd, F_SETFL, Flags | O_NONBLOCK);

    /*(3) 发送链接服务器请求*/
    if (connect(*fd, (struct sockaddr *)&servaddr, sizeof(servaddr)) < 0)
    {
        FD_ZERO(&set);
        FD_SET(*fd, &set);
        if (select((*fd) + 1, NULL, &set, NULL, &timeout) > 0)
        {
            if (func_detect_tcp_link(*fd) == -1)
                return -1;
            else
                value = 0;
        }
        else
        {
            value = -1;
            return value;
        }
    }
    return value;
}

/*
 * @description  : TCP接收数据函数
 * @param - fd   : 链接文件描述符
 * @param - *p_receive_buff: 接收数据缓冲区首地址
 * @param - count: 最大接收长度
 * @return		 : 实际接收数据长度
 */
int func_tcp_receive(int fd, char *p_receive_buff, int count)
{
    int receive_numb = 0;

    memset(p_receive_buff, 0x0, count);
    receive_numb = recv(fd, p_receive_buff, count, 0);
    if (receive_numb)
        printf("[TCP RECEIVE]=%s\n", p_receive_buff);

    return receive_numb;
}

/*
 * @description  : TCP客户端发送数据函数
 * @param - fd   : 链接文件描述符
 * @param - *p_send_buff: 发送数据缓冲区首地址
 * @param - count: 发送数据长度
 * @return		 : 实际发送数据长度
 */
int func_tcp_send(int fd, char *p_send_buff, int count)
{
    int send_numb = 0;
    if (fd <= 0)
    {
        if (resend_len + count < sizeof(resend_buff))
        {
            memcpy(resend_buff + resend_len, p_send_buff, count);
            resend_len += count;
        }

        printf("fd err\n");
        return -1;
    }

    if (func_detect_tcp_link(fd) != 0)
    {
        printf("ReadCwnd judge connect braek\n");
        if (resend_len + count < sizeof(resend_buff))
        {
            memcpy(resend_buff + resend_len, p_send_buff, count);
            resend_len += count;
        }
        return -1;
    }
    send_numb = write(fd, p_send_buff, count);
    if (count != send_numb)
    {
        if (resend_len + count < sizeof(resend_buff))
        {
            memcpy(resend_buff + resend_len, p_send_buff, count);
            resend_len += count;
        }

        printf("server terminated prematurely write err\n");
        return -1;
    }
    printf("[TCP SEND]=%s\n", p_send_buff);
    return send_numb;
}

/*
 * @description  : TCP客户端关闭链接
 * @param - fd   : 链接文件描述符
 * @return		 : 无
 */
void func_close_tcp_link(int fd)
{
    if (fd > 0)
    {
        close(fd);
    }
}

/*
 * @description  : 建立TCP服务端监听
 * @param - *fd  : 监听文件描述符
 * @param - listen_port: 监听端口
 * @return		 : 执行结果 创建失败返回-1 正常返回0
 */
int func_create_tcp_server_listen(int *fd, unsigned int listen_port)
{
    int value = 0, Flags = 0;
    struct sockaddr_in servaddr;

    /*(1) 创建套接字*/
    if ((*fd = socket(AF_INET, SOCK_STREAM, 0)) == -1)
    {
        return -1;
    }

    // 设置为非阻塞
    Flags = fcntl(*fd, F_GETFL, 0);
    fcntl(*fd, F_SETFL, Flags | O_NONBLOCK);
    setsockopt(*fd, SOL_SOCKET, SO_REUSEADDR, &Flags, sizeof(int));

    /*(2) 设置服务器sockaddr_in结构*/
    bzero(&servaddr, sizeof(servaddr));
    servaddr.sin_family = AF_INET;
    servaddr.sin_addr.s_addr = htonl(INADDR_ANY); //
    servaddr.sin_port = htons(listen_port);

    /*(3) 绑定套接字和端口*/
    if (bind(*fd, (struct sockaddr *)&servaddr, sizeof(struct sockaddr)) == -1)
    {
        printf("Bind error%s\n", strerror(errno));
        value = -1;
        return value;
    }

    /*(4) 监听客户请求*/
    if (listen(*fd, 10) < 0)
    {
        printf("listen error%s\n", strerror(errno));
        value = -1;
        return value;
    }

    value = 1;
    return value;
}

/*
 * @description  : TCP服务端与客户端建立链接
 * @param - listen_fd: 监听文件描述符
 * @param - *fd  : 链接文件描述符
 * @param - *ip  : 客户端ip，用于ip过滤
 * @return		 : 执行结果 无错误返回1 出现错误返回-1
 */
int func_tcp_server_accept(int listen_fd, int *fd, char *ip)
{
    struct sockaddr_in clientddr;
    socklen_t clilen = 0;
    int tempfd = 0, Flags = 0, value = 0;
    char temp_ip[16] = {0};
    unsigned int temp_port;

    bzero(&clientddr, sizeof(clientddr));
    clilen = sizeof(clientddr);
    // 检测跟客户端建立链接是否成功
    if ((tempfd = accept(listen_fd, (struct sockaddr *)&clientddr, &clilen)) < 0)
    {
        return value;
    }

    // 检测建立链接的客户端IP是否与设置的相同，此处不判别，将接收所有IP请求建立链接。
    memcpy(temp_ip, inet_ntoa(clientddr.sin_addr), 16);
    temp_port = ntohs(clientddr.sin_port);
    printf("client: IP=%s, port=%d\n", temp_ip, temp_port);
    if (memcmp(ip, temp_ip, 16) != 0)
    {
        close(tempfd);
        return value;
    }

    if (*fd > 0)
    {
        close(*fd);
    }
    // 设置为非阻塞
    Flags = fcntl(tempfd, F_GETFL, 0);
    fcntl(tempfd, F_SETFL, Flags | O_NONBLOCK);

    *fd = tempfd;
    value = 1;
    return value;
}
/*
 * @description  : TCP服务端关闭监听
 * @param - fd   : 监听文件描述符
 * @return		 : 无
 */
void func_close_tcp_server_listen(int listen_fd)
{
    if (listen_fd > 0)
    {
        close(listen_fd);
    }
}
/*
 * @description : 网络客户端连接管理线程
 * @param - arg : 参数
 * @return		: 无
 */
void *net_client_manage(void *arg)
{
    int result = 0;
    int connect_state = DISCONNECT;
    int wait_time = 500; // 默认每个函数间隔200ms
    int signo;
    char fram_buff[512] = {0};
    int fd = 0;
    short revents = 0;

    // TCP做客户端，主动跟服务端建立链接
    result = func_create_tcp_client_link(&sockfd, "*************", 8000);
    if (result >= 0)
    {
        connect_state = CONNECT;
        printf("tcp connected\n");
        net_inter_send_event_to_main(NET_CONNECT_OK);
    }
    else
        sockfd = 0;
    while (1)
    {
        struct pollfd pollfds[] = {{net_inter_signal_fd[1], POLLIN, 0}, {sockfd, POLLIN, 0}};
        int ne, ret, nevents = 0;
        if (sockfd > 0)
            nevents = sizeof(pollfds) / sizeof(pollfds[0]);
        else
            nevents = 1;
        do
        {
            ret = poll(pollfds, nevents, wait_time);
        } while ((ret < 0) && (errno == EINTR));

        if (ret < 0)
        {
            printf("%s poll=%d, errno: %d (%s)", __func__, ret, errno, strerror(errno));
            net_inter_send_event_to_main(NET_THREAD_QUITE);
            goto __net_manage_quit;
        }
        if (ret == 0)
        {
            if (connect_state == DISCONNECT)
            {
                // TCP做客户端，主动跟服务端建立链接
                result = func_create_tcp_client_link(&sockfd, "*************", 8000);
                if (result >= 0)
                {
                    connect_state = CONNECT;
                    printf("tcp connected\n");
                    net_inter_send_event_to_main(NET_CONNECT_OK);
                    // 发送之前缓存的数据
                    if (resend_len > 0)
                    {
                        result = func_tcp_send(sockfd, resend_buff, resend_len);
                        if (result < 0)
                        {
                            printf("send err\n");
                            connect_state = DISCONNECT;
                            close(sockfd);
                            sockfd = 0;
                            continue;
                        }
                        else
                        {
                            resend_len = 0;
                        }
                    }
                }
                else
                    sockfd = 0;
            }
            else
            {
                // 检测TCP链接状态
                result = func_detect_tcp_link(sockfd);
                if (result < 0)
                {
                    connect_state = DISCONNECT;
                    close(sockfd);
                    sockfd = 0;
                    printf("ReadCwnd judge connect braek\n");
                    // TCP做客户端，主动跟服务端建立链接
                    result = func_create_tcp_client_link(&sockfd, "*************", 8000);
                    if (result >= 0)
                    {
                        connect_state = CONNECT;
                        printf("tcp connected\n");
                        if (resend_len > 0)
                        {
                            result = func_tcp_send(sockfd, resend_buff, resend_len);
                            if (result < 0)
                            {
                                printf("send err\n");
                                connect_state = DISCONNECT;
                                close(sockfd);
                                sockfd = 0;
                                continue;
                            }
                            else
                            {
                                resend_len = 0;
                            }
                        }
                    }
                    else
                        sockfd = 0;
                }
            }
            continue;
        }

        for (ne = 0; ne < nevents; ne++)
        {
            fd = pollfds[ne].fd;
            revents = pollfds[ne].revents;

            if (fd == net_inter_signal_fd[1])
            {
                if (revents & (POLLERR | POLLHUP | POLLNVAL))
                {
                    printf("%s poll err/hup \n", __func__);
                    printf("epoll fd = %d, events = 0x%04x\n", fd, revents);
                    if (revents & (POLLHUP | POLLNVAL))
                    {
                        net_inter_send_event_to_main(NET_THREAD_QUITE);
                        goto __net_manage_quit;
                    }
                }
                if ((revents & POLLIN) == 0)
                    continue;

                if (read(fd, &signo, sizeof(signo)) == sizeof(signo))
                {
                    switch (signo)
                    {
                    case NET_QUITE_EVENT:
                    {
                        printf("net manage receive NET_QUITE_EVENT \n");
                        goto __net_manage_quit;
                    }
                    break;

                    default:
                        break;
                    }
                }
            }
            if ((fd == sockfd) && (sockfd != 0))
            {
                if (revents & (POLLERR | POLLHUP | POLLNVAL))
                {
                    connect_state = DISCONNECT;
                    close(sockfd);
                    sockfd = 0;
                    printf("poll judge connect braek\n");
                    // TCP做客户端，主动跟服务端建立链接
                    result = func_create_tcp_client_link(&sockfd, "*************", 8000);
                    if (result >= 0)
                    {
                        connect_state = CONNECT;
                        printf("tcp connected\n");
                        if (resend_len > 0)
                        {
                            result = func_tcp_send(sockfd, resend_buff, resend_len);
                            if (result < 0)
                            {
                                printf("send err\n");
                                connect_state = DISCONNECT;
                                close(sockfd);
                                sockfd = 0;
                                continue;
                            }
                            else
                            {
                                resend_len = 0;
                            }
                        }
                    }
                    else
                        sockfd = 0;
                }
                if (revents & POLLIN)
                {
                    memset(fram_buff, 0x0, sizeof(fram_buff));
                    func_tcp_receive(sockfd, fram_buff, 500);
                    if (strstr(fram_buff, "self check is end,result is FAILED") != NULL)
                    {
                        printf(L_RED "%s" NONE "\n", "B self check is end,result is FAILED ! ! !;");
                        net_inter_send_event_to_main(NET_RX_CHECK_FAIL);
                    }
                }
            }
        }
    }
__net_manage_quit:
    printf("%s exit", __func__);

    if (sockfd > 0)
    {
        close(sockfd);
        sockfd = 0;
    }
    pthread_exit(NULL);
    return NULL;
}
/*
 * @description : 网络服务端连接管理线程
 * @param - arg : 参数
 * @return		: 无
 */
void *net_server_manage(void *arg)
{
    int result = 0;
    int connect_state = DISCONNECT;
    int wait_time = 500; // 默认每个函数间隔200ms
    int signo;
    char fram_buff[512] = {0};
    int fd = 0;
    short revents = 0;

    // TCP做服务端，建立监听
    result = func_create_tcp_server_listen(&listenfd, 8000);
    if (result > 0)
    {
        printf("tcp create server listen ok\n");
    }
    else
    {
        listenfd = 0;
        if (sockfd)
        {
            close(sockfd);
            sockfd = 0;
        }
        printf("tcp create server listen err\n");
        goto __net_server_manage_quit;
    }
    while (1)
    {
        struct pollfd pollfds[] = {{net_inter_signal_fd[1], POLLIN, 0}, {listenfd, POLLIN, 0}, {sockfd, POLLIN, 0}};
        int ne, ret, nevents = 0;
        if ((sockfd > 0) && (listenfd > 0))
            nevents = sizeof(pollfds) / sizeof(pollfds[0]);
        else if (listenfd > 0)
            nevents = 2;
        else
            nevents = 1;
        do
        {
            ret = poll(pollfds, nevents, wait_time);
        } while ((ret < 0) && (errno == EINTR));

        if (ret < 0)
        {
            printf("%s poll=%d, errno: %d (%s)", __func__, ret, errno, strerror(errno));
            net_inter_send_event_to_main(NET_THREAD_QUITE);
            goto __net_server_manage_quit;
        }
        if (ret == 0)
        {
            if (!listenfd)
            {
                result = func_create_tcp_server_listen(&listenfd, 8000);
                if (result > 0)
                {
                    printf("tcp create server listen ok\n");
                }
                else
                {
                    listenfd = 0;
                    if (sockfd)
                    {
                        close(sockfd);
                        sockfd = 0;
                    }
                    printf("tcp create server listen err\n");
                }
            }
            // 检测TCP链接状态
            if (connect_state == CONNECT)
            {
                result = func_detect_tcp_link(sockfd);
                if (result < 0)
                {
                    connect_state = DISCONNECT;
                    close(sockfd);
                    sockfd = 0;
                    printf("ReadCwnd judge connect braek\n");
                }
            }

            continue;
        }

        for (ne = 0; ne < nevents; ne++)
        {
            fd = pollfds[ne].fd;
            revents = pollfds[ne].revents;

            if (fd == net_inter_signal_fd[1])
            {
                if (revents & (POLLERR | POLLHUP | POLLNVAL))
                {
                    printf("%s poll err/hup", __func__);
                    printf("epoll fd = %d, events = 0x%04x", fd, revents);
                    if (revents & (POLLHUP | POLLNVAL))
                    {
                        net_inter_send_event_to_main(NET_THREAD_QUITE);
                        goto __net_server_manage_quit;
                    }
                }

                if ((revents & POLLIN) == 0)
                    continue;

                if (read(fd, &signo, sizeof(signo)) == sizeof(signo))
                {
                    switch (signo)
                    {
                    case NET_QUITE_EVENT:
                    {
                        goto __net_server_manage_quit;
                    }
                    break;

                    default:
                        break;
                    }
                }
            }
            if ((fd == listenfd) && (listenfd != 0))
            {
                if (revents & POLLIN)
                {
                    // 接受客户端建立链接
                    result = func_tcp_server_accept(listenfd, &sockfd, "192.168.0.231");
                    if (result > 0)
                    {
                        connect_state = CONNECT;
                        printf("tcp connected\n");
                    }
                }
                if (revents & (POLLERR | POLLNVAL))
                {
                    // socket错误
                    printf("Listen socket error\n");
                    listenfd = 0;
                    result = func_create_tcp_server_listen(&listenfd, 8000);
                    if (result > 0)
                    {
                        printf("tcp create server listen ok\n");
                    }
                    else
                    {
                        listenfd = 0;
                        if (sockfd)
                        {
                            close(sockfd);
                            sockfd = 0;
                        }
                        printf("tcp create server listen err\n");
                    }
                }
            }

            if ((fd == sockfd) && (sockfd != 0))
            {
                if (revents & (POLLERR | POLLHUP | POLLNVAL))
                {
                    // socket错误
                    printf("client socket error\n");
                    close(sockfd);
                    sockfd = 0;
                    connect_state = DISCONNECT;
                }

                if (revents & POLLIN)
                {
                    memset(fram_buff, 0x0, sizeof(fram_buff));
                    func_tcp_receive(sockfd, fram_buff, 500);
                    if (strstr(fram_buff, "start self check") != NULL)
                    {
                        net_inter_send_event_to_main(NET_RX_START_SELF_CHECK);
                    }
                    if (strstr(fram_buff, "self check is PASSED") != NULL)
                    {
                        printf(L_GREEN "%s" NONE "\n", fram_buff);
                    }
                    if (strstr(fram_buff, "self check is FAILED") != NULL)
                    {
                        printf(L_RED "%s" NONE "\n", fram_buff);
                    }
                    if (strstr(fram_buff, "self check is end,result is FAILED") != NULL)
                    {
                        printf(L_RED "%s" NONE "\n", "A interact check is end,result is FAILED ! ! !;");
                        net_inter_send_event_to_main(NET_RX_CHECK_FAIL);
                    }
                    if (strstr(fram_buff, "start test") != NULL)
                    {
                        printf(L_GREEN "%s" NONE "\n", "A interact check is end,result is PASSED,start test;");
                        net_inter_send_event_to_main(NET_RX_START_TEST);
                    }
                    if (strstr(fram_buff, "B can exe next circle") != NULL)
                    {
                        net_inter_send_event_to_main(NET_RX_TEST_NEXT);
                    }
                }
            }
        }
    }

__net_server_manage_quit:
    printf("%s exit", __func__);
    if (listenfd > 0)
    {
        close(listenfd);
        listenfd = 0;
    }

    if (sockfd > 0)
    {
        close(sockfd);
        sockfd = 0;
    }
    pthread_exit(NULL);
    return NULL;
}
