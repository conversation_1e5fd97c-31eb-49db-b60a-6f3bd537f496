#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <signal.h>
#include <pthread.h>
#include <poll.h> // 添加poll头文件
#include "file.h"
#include "config_file.h"
#include "pidfile.h"
#include "manage.h"
#include "display.h"
#include "net_manage.h"

char ver[20] = {"ver03.02.01"}; // 测试程序版本

// 日志文件

struct_test *test;
struct_test_info info;
struct_cpu_employ old_cpu_employ;

FILE *pLogFile = NULL, *pInfoFile = NULL, *pResultFile = NULL, *pDataFile = NULL;
char log_file[MAX_PATH_LEN] = {"result.log"};
char result_file[MAX_PATH_LEN] = {"result.csv"};
char data_file[MAX_PATH_LEN] = {"data.csv"};
char info_file[MAX_PATH_LEN] = {"info.csv"};
char pid_file[MAX_PATH_LEN] = {"test_start.pid"};
char count_file[MAX_PATH_LEN] = {"count.txt"};
char log_path[MAX_PATH_LEN] = {0};
char temp_path[MAX_PATH_LEN] = {0};
char item_path[MAX_PATH_LEN] = {0};
char tool_path[MAX_PATH_LEN] = {0};
char conf_path[MAX_PATH_LEN] = {0};

int order_test_status = 0;
pthread_t concurrent_test_thread[MAX_TEST_ITEM] = {0};
int concurrent_test_signal_fd[MAX_TEST_ITEM][2];
pthread_t order_test_thread, response_thread, net_inter_client_thread, net_inter_server_thread;
int order_test_signal_fd[2];
int response_signal_fd[2];
int net_inter_signal_fd[2];

long sync_len = 0;
int led_value = 0, led_flag = 0;
int printf_time = 0;

pthread_mutex_t mutex_lock;

/*
 * @description : 自定义打印函数
 * @param - buff: 打印数据缓冲区
 * @param - lens: 打印数据长度
 * @param - mode: 打印格式
 * @return		: 无
 */
void func_my_print(char *buff, unsigned int lens, unsigned char mode)
{
    int i = 0;

    switch (mode)
    {
    case 'H': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("0X%02X  ", buff[i]);
        }
        break;
    case 'h': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("0x%02x  ", buff[i]);
        }
        break;
    case 'd': // 按照10进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("%02d  ", buff[i]);
        }
        break;
    case 'c': // 按照字符打印数据
        for (i = 0; i < lens; i++)
        {
            printf("%c", buff[i]);
        }
        break;
    default:
        break;
    }
    printf("\n");
}
/*
 * @description    : 给线程发送信号,主要用途是通知线程回收资源
 * @param - event  : 信号
 * @return		   : 执行结果
 */
void main_send_event_to_order_test(int event)
{
    if (write(order_test_signal_fd[0], &event, sizeof(event)) == -1)
    {
        printf("send signal to order test err\n");
    };
}
void main_send_event_to_response_test(int event)
{
    if (write(response_signal_fd[0], &event, sizeof(event)) == -1)
    {
        printf("send signal to response test err\n");
    };
}
void main_send_event_to_concurrent_tes(int num, int event)
{
    if (write(concurrent_test_signal_fd[num][0], &event, sizeof(event)) == -1)
    {
        printf("send signal to concurrent test err\n");
    };
}
/*
 * @description    : 给网络线程发送信号,主要用途是通知线程回收资源
 * @param - event  : 信号
 * @return		   : 执行结果
 */
void main_send_event_to_net_inter(int event)
{
    if (write(net_inter_signal_fd[0], &event, sizeof(event)) == -1)
    {
        printf("send signal to net inter err\n");
    };
}
/*
 * @description    : 给网络线程发送信号,主要用途是通知主流程有命令
 * @param - event  : 信号
 * @return		   : 执行结果
 */
void net_inter_send_event_to_main(int event)
{
    printf("net_inter_send_event_to_main event=%d\n", event);
    if (write(net_inter_signal_fd[1], &event, sizeof(event)) == -1)
    {
        printf("net send signal to main err\n");
    };
}
/*
 * @description    : 回收处理函数
 * @return		   : 执行结果
 */
void destroy_handle(void)
{
    int i = 0;
    int f_fd = 0;

    Func_test_report(pResultFile, test, &info);
    pthread_mutex_destroy(&mutex_lock);
    // 回收线程
    if (order_test_thread)
    {
        main_send_event_to_order_test(QUITE_EVENT);
    }
    if (response_thread)
    {
        main_send_event_to_response_test(QUITE_EVENT);
    }
    if (net_inter_client_thread)
    {
        main_send_event_to_net_inter(QUITE_EVENT);
    }
    if (net_inter_server_thread)
    {
        main_send_event_to_net_inter(QUITE_EVENT);
    }
    for (i = 0; i < MAX_TEST_ITEM; i++)
    {
        if (concurrent_test_thread[i])
        {
            main_send_event_to_concurrent_tes(i, QUITE_EVENT);
        }
    }
    sleep(3);
    if (net_inter_signal_fd[0])
        close(net_inter_signal_fd[0]);
    if (net_inter_signal_fd[1])
        close(net_inter_signal_fd[1]);
    if (order_test_signal_fd[0])
        close(order_test_signal_fd[0]);
    if (order_test_signal_fd[1])
        close(order_test_signal_fd[1]);
    if (response_signal_fd[0])
        close(response_signal_fd[0]);
    if (response_signal_fd[1])
        close(response_signal_fd[1]);
    if (pLogFile != NULL)
    {
        fflush(pLogFile); /*将数据同步至ROM*/
        f_fd = fileno(pLogFile);
        fsync(f_fd);
        file_close(pLogFile); /*关闭文件*/
    }
    if (pInfoFile != NULL)
    {
        fflush(pInfoFile); /*将数据同步至ROM*/
        f_fd = fileno(pInfoFile);
        fsync(f_fd);
        file_close(pInfoFile); /*关闭文件*/
    }
    if (pResultFile != NULL)
    {
        fflush(pResultFile); /*将数据同步至ROM*/
        f_fd = fileno(pResultFile);
        fsync(f_fd);
        file_close(pResultFile); /*关闭文件*/
    }
    if (pDataFile != NULL)
    {
        fflush(pDataFile); /*将数据同步至ROM*/
        f_fd = fileno(pDataFile);
        fsync(f_fd);
        file_close(pDataFile); /*关闭文件*/
    }
    remove_pid(pid_file);
    usleep(100000);
    free(test);
}
/*
 * @description    : 信号处理函数
 * @param - iSignNo: 信号
 * @return		   : 执行结果
 */
void SignHandler(int iSignNo)
{
    destroy_handle();
    exit(0);
}
/*
 * @description  : 补全所需目录路径
 * @param - *path: 带入参数数组指针
 * @return		 : 无
 */
void bsp_complete_path(char *path)
{
    sprintf(log_path, "%s/log/", path);
    sprintf(temp_path, "%s/temp/", path);
    sprintf(item_path, "%s/item/", path);
    sprintf(tool_path, "%s/tool/", path);
}
/*
 * @description  : 检查配置文件
 * @param - *path: 带入配置文件路径
 * @param - *app_path: 带入应用程序路径
 * @return		 : 执行结果
 */
int bsp_check_conf_file(char *path, char *app_path)
{
    char temp_path[MAX_PATH_LEN] = {0};
    sprintf(temp_path, "%s/stress_test_A.conf", app_path);
    if (access(temp_path, 0) != -1)
    {
        strcpy(path, temp_path);
        return 0;
    }
    memset(temp_path, 0, sizeof(temp_path));
    sprintf(temp_path, "%s/stress_test_B.conf", app_path);
    if (access(temp_path, 0) != -1)
    {
        strcpy(path, temp_path);
        return 0;
    }
    return -1;
}
/*
 * @description      : 检查是否有交互测试
 * @param - *manag   : 测试管理结构体
 * @return		     : 执行结果
 */
int bsp_check_interact(struct_test *manag)
{
    if ((manag->item[CAN].circle != 0) || (manag->item[RS485].circle != 0) || (manag->item[RS232].circle != 0) || (manag->item[NET].circle != 0))
        return 1;
    else
        return 0;
}
/*
 * @description      : 获取info信息，并存入文件
 * @param - *manag   : 测试管理结构体
 * @return		     : 无
 */
void get_info_and_save(struct_test *manag)
{
    char information[300] = {0};
    // 准备信息表头
    memset(information, 0, sizeof(information));
    sprintf(information, "%s,%s,%s,%s,%s\n", "time", "temperature", "frequency", "cpu_employ", "mem_employ");
    fwrite(information, sizeof(char), strlen(information), pInfoFile);

    // 获取cpu信息文件
    get_cpu_info(manag->info_path, info.cpu_info);
    // 获取2个温限
    get_temp_limit(manag->limit0_path, manag->limit1_path, &info.limit0, &info.limit1, manag->temp_coef);
    // 获取频率列表
    get_list_freq(manag->list_freq_path, info.list_freq);
    // 获取CPU个数
    get_cpus(&info.cpus);
    // 获取温度值
    get_temperature_info(manag->temp_path, &info.temperature, manag->temp_coef);
    // 获取频率值
    get_freq_info(manag->freq_path, &info.freq);
    // 获取CPU占用率
    get_cpu_employ("/proc/stat", &info.cpu_employ);
    // 获取内存占用率
    get_mem_employ("/proc/meminfo", &info.mem_employ, NULL);
    // 获取硬盘读写速率
    // get_tps_r_w(info.dev, &info.tps, &info.kB_read, &info.kB_wrtn);

    // 将cpu信息写入到日志，结果表单并打印
    file_write_data(pLogFile, info.cpu_info, strlen(info.cpu_info));
    file_write_data(pResultFile, info.cpu_info, strlen(info.cpu_info));
    printf("[cpu info] : %s\n", info.cpu_info);
    // 将温限写入日志，结果表单并打印
    memset(information, 0, sizeof(information));
    sprintf(information, "Freq reduction temp limit:%d,Reset temp limit:%d\n", info.limit0, info.limit1);
    file_write_data(pLogFile, information, strlen(information));
    file_write_data(pResultFile, information, strlen(information));
    printf("%s\n", information);
    // 将频率列表写入日志，结果表单并打印
    memset(information, 0, sizeof(information));
    sprintf(information, "%s,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d\n", "Freq list :", info.list_freq[0], info.list_freq[1], info.list_freq[2], info.list_freq[3], info.list_freq[4], info.list_freq[5], info.list_freq[6], info.list_freq[7], info.list_freq[8], info.list_freq[9]);
    file_write_data(pLogFile, information, strlen(information));
    file_write_data(pResultFile, information, strlen(information));
    printf("%s\n", information);
    // 将cpu个数写入日志，结果表单并打印
    memset(information, 0, sizeof(information));
    sprintf(information, "%s,%d\n\n", "cpus :", info.cpus);
    file_write_data(pLogFile, information, strlen(information));
    file_write_data(pResultFile, information, strlen(information));
    printf("%s\n", information);
    fflush(pResultFile); /*将数据同步至ROM*/
}
/*
 * @description      : 为即将创建的线程，创建socketpair，用以线程间传递信号
 * @param - flag     : 测试模式
 * @return		     : 执行结果
 */
int create_socketpair(int flag)
{
    int i = 0;
    if ((SPONSOR_IN_ORDER == flag) || (RESPONSE_IN_ORDER == flag))
    {
        if (socketpair(AF_LOCAL, SOCK_STREAM, 0, order_test_signal_fd) < 0)
        {
            printf("%s Faild to create order_test_signal_fd: %d (%s)", __func__, errno, strerror(errno));
            return -1;
        }
    }
    if ((RESPONSE_IN_ORDER == flag) || (RESPONSE_CONCURRENT == flag))
    {
        if (socketpair(AF_LOCAL, SOCK_STREAM, 0, response_signal_fd) < 0)
        {
            printf("%s Faild to create response_signal_fd: %d (%s)", __func__, errno, strerror(errno));
            return -1;
        }
    }
    if ((SPONSOR_CONCURRENT == flag) || (RESPONSE_CONCURRENT == flag)) // 并行测试
    {
        for (i = 0; i < MAX_TEST_ITEM; i++)
        {
            if (socketpair(AF_LOCAL, SOCK_STREAM, 0, concurrent_test_signal_fd[i]) < 0)
            {
                printf("%s Faild to create concurrent_test_signal_fd[%d]: %d (%s)", __func__, i, errno, strerror(errno));
                return -1;
            }
        }
    }
    // 创建主流程和网络交互信号通道
    if (socketpair(AF_LOCAL, SOCK_STREAM, 0, net_inter_signal_fd) < 0)
    {
        printf("%s Faild to create net_inter_signal_fd: %d (%s)", __func__, errno, strerror(errno));
        return -1;
    }
    return 0;
}
/*
 * @description      : TCP双板通信和自检部分
 * @param - *manag   : 测试管理结构体
 * @return		     : 执行结果
 */
int func_tcp_init_and_self_check(struct_test *manag)
{
    struct timeval tv;
    int triger_event = 0;
    int result = 0;
    int B_self_check_result = 1; // 默认自检成功

    // 检查是否有交互类测试项
    if (manag->interact_flag != 0)
    {
        if ((RESPONSE_IN_ORDER == manag->pattern) || (RESPONSE_CONCURRENT == manag->pattern))
        {
            if (pthread_create(&response_thread, NULL, response_test_manage, (void *)manag))
            {
                printf("Pthread response_test_manage create error\n");
                return -1;
            }
            sleep(1);
            // 创建网络服务端管理线程
            if (pthread_create(&net_inter_server_thread, NULL, net_server_manage, (void *)manag))
            {
                printf("Pthread net_server_manage create error\n");
                return -1;
            }
            tv.tv_sec = 300; // 设置超时时间为300秒
            tv.tv_usec = 0;
            setsockopt(net_inter_signal_fd[0], SOL_SOCKET, SO_RCVTIMEO, (const char *)&tv, sizeof tv);
            if (read(net_inter_signal_fd[0], &triger_event, sizeof(triger_event)) != sizeof(triger_event) || (triger_event != NET_RX_START_SELF_CHECK))
            {
                printf("net connect fail\n");
                return -1;
            }
        }
        else
        {
            // 创建网络客户端管理线程
            if (pthread_create(&net_inter_client_thread, NULL, net_client_manage, (void *)manag))
            {
                printf("Pthread net_client_manage create error\n");
                return -1;
            }
            if (read(net_inter_signal_fd[0], &triger_event, sizeof(triger_event)) != sizeof(triger_event) || (triger_event != NET_CONNECT_OK))
            {
                printf("net connect fail\n");
                return -1;
            }
            func_tcp_send(sockfd, "start self check", strlen("start self check"));
            sleep(2); // 此处等待B板把对测服务都启动完
        }
    }
    // 自检过程
    if (func_self_check(manag) != 0)
    {
        printf("self check fail\n");
        // 自检结束，出现错误，A板可直接退出，B板需要等A板发退出信号
        if (manag->interact_flag != 0)
        {
            if ((RESPONSE_IN_ORDER == manag->pattern) || (RESPONSE_CONCURRENT == manag->pattern))
            {
                B_self_check_result = 0;
            }
            else
            {
                return -1;
            }
        }
        else
        {
            return -1;
        }
    }
    // 设置socket为非阻塞
    int flags = fcntl(net_inter_signal_fd[0], F_GETFL, 0);
    fcntl(net_inter_signal_fd[0], F_SETFL, flags | O_NONBLOCK);

    if (manag->interact_flag != 0)
    {
        if ((RESPONSE_IN_ORDER == manag->pattern) || (RESPONSE_CONCURRENT == manag->pattern))
        {
            int count = 0;
            while (1)
            {
                struct pollfd pollfds[] = {{net_inter_signal_fd[0], POLLIN, 0}};
                int ret = poll(pollfds, sizeof(pollfds) / sizeof(pollfds[0]), 500);
                if (ret < 0)
                {
                    printf("poll error\n");
                    break;
                }
                if (ret == 0)
                {
                    count++;
                    if (count > 600)
                    {
                        result = -1;
                        break;
                    }
                    else
                        continue;
                }
                if (pollfds[0].revents & POLLIN)
                {
                    if (read(net_inter_signal_fd[0], &triger_event, sizeof(triger_event)) == sizeof(triger_event))
                    {
                        switch (triger_event)
                        {
                        case NET_RX_CHECK_FAIL:
                            result = -1;
                            break;
                        case NET_RX_START_TEST:
                            if (B_self_check_result == 0)
                                result = -1;
                            else
                                result = 1;
                            break;
                        case NET_QUITE_EVENT:
                            result = -1;
                            break;
                        default:
                            break;
                        }
                    }
                }
                if (result != 0)
                {
                    break;
                }
            }
        }
        else
        {
            // 读一次net_inter_signal_fd[0]
            int new_event = 0;
            int ret_read = read(net_inter_signal_fd[0], &new_event, sizeof(new_event));
            if (ret_read == sizeof(new_event) && new_event == NET_RX_CHECK_FAIL)
            {
                result = -1;
            }
            else
            {
                result = 1;
            }
        }
    }
    else
        result = 1;
    return result;
}
/*
 * @description  : 主函数
 * @param - argc : 参数个数
 * @param - *argv: 带入参数数组指针
 * @return		 : 执行结果
 */
int main(int argc, char *argv[])
{
    int current_test_item = 0;
    int point = 0;
    unsigned int conf_crc = 0;
    unsigned int conf_real_crc = 0;
    int ret = 0;
    int triger_event = 0;

    // 所有参数和状态清0
    test = malloc(sizeof(struct_test));
    memset((char *)test, 0, sizeof(struct_test));
    memset((char *)&info, 0, sizeof(struct_test_info));

    strcpy(test->app_path, argv[1]);
    // 检查配置文件是否存在
    if (bsp_check_conf_file(conf_path, test->app_path) == -1)
    {
        printf("conf file error\n");
        exit(1);
    }

    // 检查配置文件crc
    if (argc < 3)
    {
        // 不设置就是不检查crc，也可以继续执行，只是初始需要告警
        printf(L_RED "\n###################################################" NONE "\n");
        printf(L_RED "##  Pay attention !!! Not check config file crc  ##" NONE "\n");
        printf(L_RED "###################################################" NONE "\n");
    }
    else
    {
        conf_crc = strtoul(argv[2], NULL, 16);
        conf_real_crc = Func_get_conf_crc(conf_path);
        if (conf_real_crc != conf_crc)
        {
            printf(L_RED "conf file crc error" NONE "\n");
            exit(1);
        }
    }
    printf("type test ver : %s\n", ver);
    // 获取并检查配置参数
    if (-1 == func_get_conf(test, conf_path))
        exit(1);
    // 将命令参数解析，包含第一路ip地址的设置
    func_del_conf(test);
    // 获取文件系统挂载盘符
    if (-1 == func_get_devname(info.dev, "/ "))
    {
        printf("Can not get root directory disk");
        exit(1);
    }

    // 补全各个目录
    bsp_complete_path(test->app_path);

    // 确认log文件夹是否存在
    if ((access(log_path, 0)) == -1)
    {
        printf("not exist\n");
        mkdir(log_path, S_IRWXU);
    }

    // 确认进程是否已经运行
    file_add_full_fath(pid_file, log_path, "test_start.pid");
    pid_t ppid = getpid();
    if (!check_pid(pid_file))
    {
        // printf("pid_file=%s\n", pid_file);
        if (!write_pid(pid_file))
        {
            printf("write_file ok\n");
            if (getpid() != ppid)
                kill(ppid, SIGTERM);
            exit(1);
        }
    }
    else
    {
        if (0 == check_old_pid(pid_file)) // 没有此pid文件或此pid文件不是test_start进程
        {
            // 删除旧pid文件，写新的pid文件
            remove_pid(pid_file);
            if (!write_pid(pid_file))
            {
                if (getpid() != ppid)
                    kill(ppid, SIGTERM);
                exit(1);
            }
        }
        else
        {
            printf("test_start: Already running.\n");
            if (getpid() != ppid)
                kill(ppid, SIGTERM);
            exit(1);
        }
    }

    // 保存日志文件
    file_add_full_fath(log_file, log_path, "result.log");
    pLogFile = file_open(log_file, ATWR);
    if (NULL == pLogFile)
        goto __ToEnd;
    file_add_full_fath(info_file, log_path, "info.csv");
    pInfoFile = file_open(info_file, ATWR);
    if (NULL == pInfoFile)
        goto __ToEnd;
    file_add_full_fath(result_file, log_path, "result.csv");
    pResultFile = file_open(result_file, ATWR);
    if (NULL == pResultFile)
        goto __ToEnd;
    file_add_full_fath(data_file, log_path, "data.csv");
    pDataFile = file_open(data_file, ATWR);
    if (NULL == pDataFile)
        goto __ToEnd;

    // 打开计次文件
    file_add_full_fath(count_file, log_path, "count.txt");
    if (-1 == get_start_count(count_file, &info.run_count))
        goto __ToEnd;

    // 设置心跳灯
    set_heartbeat(test->hbset);
    // 获取info信息，并存入文件
    get_info_and_save(test);
    // 为即将创建的线程，创建socketpair，用以线程间传递信号
    ret = create_socketpair(test->pattern);
    if (ret != 0)
    {
        printf("create socketpair error\n");
        goto __ToEnd;
    }

    // 进程被kill 需要保存文件
    signal(SIGTERM, SignHandler);
    // 按下 ctrl + c 需要保存文件
    signal(SIGINT, SignHandler);

    // 打开一个互斥锁，防止并行测试线程间资源异常
    pthread_mutex_init(&mutex_lock, NULL);

    // 执行预处理命令
    exe_pre_cmd(test);

    // TCP双板通信和自检部分
    test->interact_flag = bsp_check_interact(test);
    ret = func_tcp_init_and_self_check(test);
    if (ret != 1)
    {
        printf("tcp init and self check error\n");
        goto __ToEnd;
    }

    //  准备数据表单表头
    save_data_sheet(pDataFile, test, 0);
    info.circle_count = 1;

    while (1)
    {
        if ((SPONSOR_CONCURRENT == test->pattern) || (RESPONSE_CONCURRENT == test->pattern)) // 并行测试
        {
            get_concurrent_test_status(test, &order_test_status);
            if (IDLE == order_test_status) // 起新测试进程
            {
                // 创建所有测试项管理线程
                for (point = 0; point < MAX_TEST_ITEM; point++)
                {
                    if (test->item[point].circle != 0)
                    {
                        test->item[point].numb = point;
                        if (pthread_create(&concurrent_test_thread[point], NULL, concurrent_test_manage, (void *)&test->item[point]))
                        {
                            printf("Pthread concurrent_test_manage create error\n");
                            goto __ToEnd;
                        }
                        // 将线程设置为分离状态
                        if (pthread_detach(concurrent_test_thread[point]) != 0)
                        {
                            perror("pthread_detach");
                        }
                    }
                }
                order_test_status = RUNNING;
            }
            else if (OVER == order_test_status) // 测试结束，回收测试线程
            {
                goto __ToEnd;
            }
        }
        else if ((SPONSOR_IN_ORDER == test->pattern) || (RESPONSE_IN_ORDER == test->pattern))
        {
            get_order_test_status(&order_test_status, &current_test_item, test, &info);
            if (IDLE == order_test_status) // 起新测试进程
            {
                // 创建链接管理线程
                if (pthread_create(&order_test_thread, NULL, in_order_test_manage, (void *)&test->item[current_test_item]))
                {
                    printf("Pthread in_order_test_manage create error\n");
                    goto __ToEnd;
                }
                // 将线程设置为分离状态
                if (pthread_detach(order_test_thread) != 0)
                {
                    perror("pthread_detach");
                }
                order_test_status = RUNNING;
            }
            else if (OVER == order_test_status) // 测试结束，回收测试线程
            {
                goto __ToEnd;
            }
        }
        fflush(stdout);
        if (test->interact_flag != 0)
        {
            if (read(net_inter_signal_fd[0], &triger_event, sizeof(triger_event)) == sizeof(triger_event))
            {
                switch (triger_event)
                {
                case NET_RX_TEST_NEXT:
                    printf("net_inter_send_event_to_main NET_RX_TEST_NEXT\n");
                    test->B_can_next_circle = 1;
                    break;
                case NET_QUITE_EVENT:
                    printf("net_inter_send_event_to_main NET_QUITE_EVENT\n");
                    goto __ToEnd;
                    break;
                default:
                    break;
                }
            }
        }
        sleep(1);
        if (led_flag)
            conctol_heartbeat(test->hbconctol, 1);
        else
        {
            conctol_heartbeat(test->hbconctol, led_value);
            if (led_value)
                led_value = 0;
            else
                led_value = 1;
        }
        if (0 == printf_time)
        {
            file_write_infos(pInfoFile, &info);
            // 存储数据
            save_data_sheet(pDataFile, test, 1);
        }
        printf_time++;
        printf_time %= 60;
    }
__ToEnd:
    destroy_handle();
    exit(1);
}
