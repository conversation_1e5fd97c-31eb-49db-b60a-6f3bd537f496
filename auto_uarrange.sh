#!/bin/bash
# 检查命令行参数
if [[ "$1" != "ALL" && "$1" != "PART" ]]; then
    echo "Usage: $0 ALL | PART"
    exit 1
fi

# 初始化变量
declare -A gcc_map
key_order=()

# 在脚本开始处添加日志文件路径
LOG_FILE="compile_result.log"
# 清空日志文件
> "$LOG_FILE"

# Parse gcc_all.txt file
while IFS= read -r line || [ -n "$line" ]; do
    # Skip if line starts with "#classify"
    if [[ "$line" =~ ^#classify ]]; then
        break  # Exit the loop when reaching classify section
    fi

    # 跳过空行，同时保存当前的键值对
    if [[ -z "$line" ]]; then
        if [[ -n "$current_key" && -n "$current_value" ]]; then
            gcc_map["$current_key"]="$current_value"
            current_key=""
            current_value=""
        fi
        continue
    fi

    # 如果行以#开头，表示这是一个新的键
    if [[ "$line" =~ ^#(.+) ]]; then
        # 保存之前的键值对（如果存在）
        if [[ -n "$current_key" && -n "$current_value" ]]; then
            gcc_map["$current_key"]="$current_value"
            current_value=""
        fi
        # 提取键（#后面到第一个空格之前的内容）
        current_key=$(echo "${BASH_REMATCH[1]}" | cut -d' ' -f1)
        # 将新的键添加到顺序数组中
        key_order+=("$current_key")
    else
        # 将行添加到当前值中，保持换行符
        if [[ -n "$current_value" ]]; then
            current_value+=$'\n'
        fi
        current_value+="$line"
    fi
done < gcc_all.txt

# 保存最后一个键值对（如果存在）
if [[ -n "$current_key" && -n "$current_value" ]]; then
    gcc_map["$current_key"]="$current_value"
fi

# 根据模式设置要处理的键列表
if [[ "$1" == "ALL" ]]; then
    # ALL模式：处理所有键
    keys_to_process=("${key_order[@]}")
elif [[ "$1" == "PART" ]]; then
    # PART模式：只处理指定的键
    # 在脚本中直接定义PART列表，用空格分开
    keys_to_process=("3568_5_10_160" "3588_5_10_66" "3576_6_1_57")
fi

# 获取平台对应的基础输出目录
get_output_base_dir() {
    local name=$1
    # RK platforms
    if [[ "$name" =~ ^(33|35) ]]; then
        echo "RK"
    # NXP platforms
    elif [[ "$name" =~ ^(6u|6q|6x|8m|93|10) ]]; then
        echo "NXP"
    # ALLWINNER platforms
    elif [[ "$name" =~ ^(T|A40) ]]; then
        echo "ALLWINNER"
    # LOONGSON platforms
    elif [[ "$name" =~ ^(loo) ]]; then
        echo "LOONGSON"
    # NUVOTON platforms
    elif [[ "$name" =~ ^(MA) ]]; then
        echo "NUVOTON"
    # RENESAS platforms
    elif [[ "$name" =~ ^(g2) ]]; then
        echo "RENESAS"
    # SEMDRVE platforms
    elif [[ "$name" =~ ^(D9) ]]; then
        echo "SEMDRVE"
    # TI platforms
    elif [[ "$name" =~ ^(62) ]]; then
        echo "TI"
    # Other platforms
    else
        echo "other"
    fi
}
# 清理之前编译的文件
echo "开始清理编译文件..."
for key in "${keys_to_process[@]}"; do
    echo "----------------------------------------"
    echo "正在清理 $key 平台..."

    # 确定输出目录
    base_dir=$(get_output_base_dir "$key")
    output_dir="/home/<USER>/common_tools/type_test_src_v_3_1/${base_dir}/${key}"
    upload_dir="/home/<USER>/common_tools/type_test_v_3_1_upload/${base_dir}/${key}"
    rm $output_dir/target/item/can_type
    rm $output_dir/target/item/i2c_type
    rm $output_dir/target/item/spi_type
    rm $output_dir/target/test_start
    rm $output_dir/target/item/tty_type
    rm $output_dir/target/item/wd_type
    rm $output_dir/target/test_restart.sh
    rm $output_dir/*.tar
    rm $output_dir/*.bin
    rm $output_dir/install.sh
    rm $output_dir/arrange.sh
    rm $output_dir/uarrange.sh
    rm $output_dir/one_arrange.sh
    rm $output_dir/one_uarrange.sh
    rm $upload_dir/*.bin

    echo "----------------------------------------"
done

echo "清理完成！"
