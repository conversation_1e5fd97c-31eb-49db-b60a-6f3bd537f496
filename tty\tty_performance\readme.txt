test name : can_test
test ver : sv03.001
first edition compilation time :2023/06/15

note:

1)different platforms have to choose different cross-compilation .you need to modify makefile redefine "CC".

2)The initiating test device regularly sends the specified data, and then waits for the response device to receive the data back to verify whether the received data is consistent with the sent data. 
Inconsistent will be red letter error reminder, consistent do not print information. After all test times, the error-free green letter is successful, otherwise the red letter fails.
The receiving waiting timeout is 80% of the send interval.

some more examples:
