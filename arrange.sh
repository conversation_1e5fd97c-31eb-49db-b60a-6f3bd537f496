#!/bin/bash

cp can/can_performance/can_type target/item
cp i2c/i2c_test/i2c_type target/item
cp spi/spi_test/spi_type target/item
cp stress_test/test_start target
cp tty/tty_performance/tty_type target/item
cp wd/wd_test/wd_type target/item
cp nand_check/check_nand target/item
cp A/stress_test_A.conf target
cd target
rm stress_test_B.conf
tar cvf ../target.tar *
cd ../
cat install.sh target.tar > type_test_A.bin
rm target.tar
cp B/stress_test_B.conf target
cd target
rm stress_test_A.conf
tar cvf ../target.tar *
cd ../
cat install.sh target.tar > type_test_B.bin
rm target.tar
