
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdarg.h>
#include <ctype.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include "config_file.h"

/*
 * @description : 将读出的一行，去掉多余空格
 * @param - *s  : 当前行
 * @return	    : 无
 */
char *trim_left_right(char *s)
{
    char *e;

    /* 去除开头的空白 */
    while (isspace(*s))
        s++;

    /* 结尾空白全部置为\0 */
    e = s + strlen(s) - 1;
    while (isspace(*e) && e > s)
    {
        *e = '\0';
        e--;
    }
    if (e == s)
    {
        *e = '\0';
    }

    return s;
}

/*
 * @description : 查找= 或 ：
 * @param - c   : 当前字符
 * @return	    : 有无关键字 1：有 0 无
 */
int isdelimiter(char c)
{
    if (isspace(c) || c == '=' || c == ':')
    {
        return 1;
    }
    else
    {
        return 0;
    }
}
/*
 * @description : 查找[ 或 ]
 * @param - c   : 当前字符
 * @return	    : 有无关键字 1：有 0 无
 */
int istitle(char c)
{
    if (isspace(c) || c == '[' || c == ']')
    {
        return 1;
    }
    else
    {
        return 0;
    }
}
/*
 * @description   : 通过字符串转对应测试项枚举
 * @param - *value: 文件字符串
 * @param - *item : 测试类别和字符串对应表
 * @return	      : 测试项枚举
 */
enum_test_type bsp_change_item_type(char *value, struct_name_str2numb *item)
{
    int i = 0;

    for (i = 0; i < MAX_TEST_ITEM; i++)
    {
        if (NULL != strstr(value, item->str))
            return item->numbs;
        else
            item++;
    }
    return TYPE_NO;
}
/*
 * @description         : 字符串替换函数
 * @param - *str        ：源字符串
 * @param - *sub        : 要被替换的字符串
 * @param - *replace    : 替换为的内容
 * @return	            : 无
 */
void str_replace(const char *str, const char *sub, const char *replace, char *dest)
{
    char *pos = (char *)str;

    int size = (strlen(str) - strlen(sub) + strlen(replace)) + 1;

    char *result = (char *)malloc(size);
    if (NULL == result)
        return;
    memset(result, '\0', size);
    char *current;
    current = strstr(pos, sub);
    if (NULL != current)
    {
        int len = current - pos;
        strncat(result, pos, len);
        strncat(result, replace, strlen(replace));
        pos = current + strlen(sub);
    }

    if (pos != (str + strlen(str)))
    {
        strncat(result, pos, (str - pos));
    }

    strcpy(dest, result);
    free(result);
    return;
}
/*
 * @description       : 获取设置类型AT指令的命令
 * @param - set_order : 设置AT信息存放结构体
 * @param - *key      : 当前读出的key
 * @param - *value    : 当前读出的value
 * @return	          : 无
 */
void bsp_gain_set_param(struct_test_item *set_order, char *key, char *value, char *app_path)
{
    int i = 0, j = 0, k = 0;
    char temp_buf[20] = {0};
    int param[MAX_VALUE_NUMB] = {0};
    int data = 0;

    if (NULL != strstr(key, "circle"))
    {
        data = atoi(value);
        if (data > 0)
            set_order->circle = data;
    }

    for (j = 0; j < MAX_GROUP; j++)
    {
        for (i = 0; i < MAX_EXE_ITEM; i++)
        {
            memset(temp_buf, 0, sizeof(temp_buf));
            sprintf(temp_buf, "group%dorder%d", (j + 1), (i + 1));
            if (NULL != strstr(key, temp_buf))
            {
                memset(set_order->group[j].cmd[i].exe, 0, MAX_CONTENT_LEN);
                while (strstr(value, "@app_path"))
                {
                    str_replace(value, "@app_path", app_path, value);
                }
                strcpy(set_order->group[j].cmd[i].exe, value);

                if ((i + 1) > set_order->group[j].numb)
                    set_order->group[j].numb = i + 1;
                if ((j + 1) > set_order->groups)
                    set_order->groups = j + 1;
            }
            memset(temp_buf, 0, sizeof(temp_buf));
            sprintf(temp_buf, "group%dparam%d", (j + 1), (i + 1));
            if (NULL != strstr(key, temp_buf))
            {
                sscanf(value, "%d %d %d %d %d %d %d %d %d %d", &param[0], &param[1], &param[2], &param[3], &param[4], &param[5], &param[6], &param[7], &param[8], &param[9]);
                while (param[k])
                {
                    set_order->group[j].cmd[i].value_numb = k + 1;
                    set_order->group[j].cmd[i].result_data[k].stage_param = param[k];
                    k++;
                }
            }
            memset(temp_buf, 0, sizeof(temp_buf));
            sprintf(temp_buf, "group%dtimeout%d", (j + 1), (i + 1));
            if (NULL != strstr(key, temp_buf))
            {
                data = atoi(value);
                if (data > 0)
                    set_order->group[j].cmd[i].timeout = data;
            }
        }
    }
}
/*
 * @description             : 设置命令，把要设置的内容替换为目的内容
 * @param - *set_order      : 待替换内容的设置命令
 * @return	                : 无
 */
void bsp_set_item_replace(struct_test_item *set_order)
{
    char data_str[20] = {0};
    char temp_order[MAX_CONTENT_LEN] = {0};
    int i = 0, j = 0;

    sprintf(data_str, "%d%c", 1, 'M');

    // 根据待替换格式，实现将格式替换为目的字符串
    for (j = 0; j < set_order->groups; j++)
    {
        for (i = 0; i < set_order->group[j].numb; i++)
        {
            if (NULL != strstr(set_order->group[j].cmd[i].exe, "@mem"))
            {
                memset(temp_order, 0, MAX_CONTENT_LEN);
                str_replace(set_order->group[j].cmd[i].exe, "@mem", data_str, temp_order);
                memset(set_order->group[j].cmd[i].exe, 0, MAX_CONTENT_LEN);
                strcpy(set_order->group[j].cmd[i].exe, temp_order);
            }
        }
    }
}
/*
 * @description       : 获取指令
 * @param - test_type : 测试项
 * @param - *key      : 当前读出串的key
 * @param - *value    : 当前读出串的value
 * @param - *manag    : 模块管理结构体
 * @return	          : 无
 */
void bsp_get_at_order(enum_test_type test_type, char *key, char *value, struct_test *manag)
{
    int data = 0;
    int i;
    char cmd[MAX_CONTENT_LEN] = {0};

    switch (test_type)
    {
    case TYPE_NO:
        if (NULL != strstr(key, "pattern"))
        {
            data = atoi(value);
            if (data >= 0)
                manag->pattern = data;
        }
        if (NULL != strstr(key, "whole_test_circle"))
        {
            data = atoi(value);
            if (data > 0)
                manag->circles = data;
            else
                manag->circles = -1;
        }
        if (NULL != strstr(key, "test_interval"))
        {
            data = atoi(value);
            if (data > 0)
                manag->interval = data;
            else
                manag->interval = 20;
        }
        if (NULL != strstr(key, "temperature_limit"))
        {
            data = atoi(value);
            if (data > 0)
                manag->temp_limit = data;
            else
                manag->temp_limit = 105;
        }
        if (NULL != strstr(key, "temperature_coef"))
        {
            data = atoi(value);
            if (data > 0)
                manag->temp_coef = data;
            else
                manag->temp_coef = 105;
        }
        // if (NULL != strstr(key, "app_path"))
        //{
        //  memset(manag->app_path, 0, MAX_CONTENT_LEN);
        // strcpy(manag->app_path, value);
        //}
        if (NULL != strstr(key, "info_path"))
        {
            memset(manag->info_path, 0, MAX_CONTENT_LEN);
            strcpy(manag->info_path, value);
        }
        if (NULL != strstr(key, "info_key"))
        {
            memset(manag->info_key, 0, 20);
            strcpy(manag->info_key, value);
        }
        if (NULL != strstr(key, "temp_path"))
        {
            memset(manag->temp_path, 0, MAX_CONTENT_LEN);
            strcpy(manag->temp_path, value);
        }
        if (NULL != strstr(key, "temp_key"))
        {
            memset(manag->temp_key, 0, 20);
            strcpy(manag->temp_key, value);
        }
        if (NULL != strstr(key, "limit0_path"))
        {
            memset(manag->limit0_path, 0, MAX_CONTENT_LEN);
            strcpy(manag->limit0_path, value);
        }
        if (NULL != strstr(key, "limit0_key"))
        {
            memset(manag->limit0_key, 0, 20);
            strcpy(manag->limit0_key, value);
        }
        if (NULL != strstr(key, "limit1_path"))
        {
            memset(manag->limit1_path, 0, MAX_CONTENT_LEN);
            strcpy(manag->limit1_path, value);
        }
        if (NULL != strstr(key, "limit1_key"))
        {
            memset(manag->limit1_key, 0, 20);
            strcpy(manag->limit1_key, value);
        }
        if (NULL != strstr(key, "freq_path"))
        {
            memset(manag->freq_path, 0, MAX_CONTENT_LEN);
            strcpy(manag->freq_path, value);
        }
        if (NULL != strstr(key, "freq_key"))
        {
            memset(manag->freq_key, 0, 20);
            strcpy(manag->freq_key, value);
        }
        if (NULL != strstr(key, "list_f_path"))
        {
            memset(manag->list_freq_path, 0, MAX_CONTENT_LEN);
            strcpy(manag->list_freq_path, value);
        }
        if (NULL != strstr(key, "list_f_key"))
        {
            memset(manag->list_freq_key, 0, 20);
            strcpy(manag->list_freq_key, value);
        }
        if (NULL != strstr(key, "hbset"))
        {
            memset(manag->hbset, 0, MAX_CONTENT_LEN);
            strcpy(manag->hbset, value);
        }
        if (NULL != strstr(key, "hbconctol"))
        {
            memset(manag->hbconctol, 0, MAX_CONTENT_LEN);
            strcpy(manag->hbconctol, value);
        }
        for (i = 1; i <= MAX_PRE_CMD_NUMB; i++)
        {
            memset(cmd, 0, MAX_CONTENT_LEN);
            sprintf(cmd, "pretreatment_cmd%d", i);
            if (NULL != strstr(key, cmd))
            {
                memset(manag->pre_cmd[i - 1], 0, MAX_CONTENT_LEN);
                strcpy(manag->pre_cmd[i - 1], value);
                manag->pre_cmd_numb = i;
            }
        }
        break;
    case SOC:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 0;
        break;
    case DDR:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        // bsp_set_item_replace(&manag->item[test_type]);
        manag->item[test_type].sub_log = 0;
        break;
    case EMMC:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 0;
        break;
    case NAND:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 0;
        break;
    case CAN:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 1;
        break;
    case RS485:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 1;
        break;
    case RS232:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 1;
        break;
    case SPI:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 1;
        break;
    case IIC:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 1;
        break;
    case NET:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 1;
        break;
    case AUDIO:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 1;
        break;
    case CAMERA:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 0;
        break;
    case SATA:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 0;
        break;
    case RTC:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 0;
        break;
    case SD:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 0;
        break;
    case USB:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 0;
        break;
    case TYPE_C:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 0;
        break;
    case G4:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 1;
        break;
    case WIFI:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 1;
        break;
    case DIDO:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 0;
        break;
    case PCIE:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 0;
        break;
    case BT:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 1;
        break;
    case DISPLAY:
        bsp_gain_set_param(&manag->item[test_type], key, value, manag->app_path);
        manag->item[test_type].sub_log = 0;
        break;
    default:
        break;
    }
}
/*
 * @description       : 检查指令内容
 * @param - *manag    : 模块管理结构体
 * @return	          : 0-正常 -1-异常
 */
int bsp_check_commend(struct_test *manag)
{
    int i = 0, j = 0, k = 0;
    // int order_len = 0;

    if (!manag->interval)
        manag->interval = 20;
    if (!manag->circles)
        manag->circles = -1;
    for (i = 0; i < MAX_TEST_ITEM; i++)
    {
        for (j = 0; j < manag->item[i].groups; j++)
        {
            if (0 != manag->item[i].circle)
            {
                for (k = 0; k < manag->item[i].group[j].numb; k++)
                {
                    // order_len = strlen(manag->item[i].group[j].cmd[k].exe);
                }
            }
        }
    }
    return 0;
}
/*
 * @description      : 通过配置文件，解析配置文件到参数里
 * @param - *path    : 参数文件
 * @param - *manag   : 测试管理结构体
 * @return	         : 正常返回0，未找到返回-1
 */
int bsp_find_test_item(char *path, struct_test *manag)
{
    FILE *temp_fp;
    char buf[MAX_CONFIG_BUF_SIZE], *key, *value, *tmp;
    struct_name_str2numb item[MAX_TEST_ITEM] = {
        {TYPE_NO, "TYPE_NO"},
        {SOC, "SOC"},
        {DDR, "DDR"},
        {EMMC, "EMMC"},
        {NAND, "NAND"},
        {CAN, "CAN"},
        {RS485, "RS485"},
        {RS232, "RS232"},
        {SPI, "SPI"},
        {IIC, "IIC"},
        {NET, "NET"},
        {AUDIO, "AUDIO"},
        {CAMERA, "CAMERA"},
        {SATA, "SATA"},
        {RTC, "RTC"},
        {SD, "SD"},
        {USB, "USB"},
        {TYPE_C, "TYPE_C"},
        {G4, "G4"},
        {WIFI, "WIFI"},
        {DIDO, "DIDO"},
        {PCIE, "PCIE"},
        {BT, "BT"},
        {DISPLAY, "DISPLAY"},
    };
    enum_test_type test_name = TYPE_NO;

    memset(buf, 0, MAX_CONFIG_BUF_SIZE);

    if ((temp_fp = fopen(path, "r")) == NULL)
    {
        fprintf(stderr, "ERROR: can't open conf file \"%s\"\n", path);
        return -1;
    }
    while (fgets(buf, MAX_CONFIG_BUF_SIZE, temp_fp) != NULL)
    {
        /* 去除#号及其之后的字符 */
        tmp = buf;
        while (*tmp != '#' && *tmp != '\0')
        {
            tmp++;
        }
        if (*tmp == '#')
        {
            *tmp = '\0';
        }

        /* 去除前后的空白符 */
        key = trim_left_right(buf);

        if (*key == '\0')
        {
            memset(buf, 0, MAX_CONFIG_BUF_SIZE);
            continue;
        }
        if (istitle(*key))
        {
            // 遇到[]，改变命令类型
            test_name = bsp_change_item_type(key, item);
        }
        else
        {
            value = key;
            /* 使用\0设置key和value之间的分隔符，即可取出key，并得到value的起始位置 */
            while (!isdelimiter(*value) && *value != '\0')
            {
                value++;
            }
            while (isdelimiter(*value) && *value != '\0')
            {
                *value = '\0';
                value++;
            }
            // 解析并存储到命令对应存储区
            bsp_get_at_order(test_name, key, value, manag);
        }

        memset(buf, 0, MAX_CONFIG_BUF_SIZE);
    }
    // if (-1 == bsp_check_commend(manag))
    //  return -1;

    fclose(temp_fp);
    return 0;
}

/*
 * @description      : 获取并解析配置文件
 * @param - *manag   : 测试管理结构体
 * @return	         : 正常返回0，未找到返回-1
 */
int func_get_conf(struct_test *manag, char *path)
{
    int ret = -1;

    ret = bsp_find_test_item(path, manag);
    return ret;
}
