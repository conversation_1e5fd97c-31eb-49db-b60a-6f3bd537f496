#335xD
# CC=/usr/local/arm/cross/am335xt3/devkit/bin/arm-arago-linux-gnueabi-gcc
#A40i
# CC=/usr/opt/arm/opt/ext-toolchain/bin/arm-linux-gnueabihf-gcc 
#3568
# CC=aarch64-linux-gnu-gcc

can_type:main.o can.o canfd.o file.o 
	$(CC)	-Wall	main.o can.o canfd.o file.o -o	can_type
main.o:main.c mycan.h file.h display.h
	$(CC)	-c	-Wall	main.c	-o	main.o
can.o:can.c mycan.h
	$(CC)	-c	-Wall	can.c	-o	can.o
canfd.o:canfd.c mycan.h
	$(CC)	-c	-Wall	canfd.c	-o	canfd.o
file.o:file.h mycan.h
	$(CC)	-c	-Wall	file.c	-o	file.o
clean:
	$(RM) *.o	can_type

