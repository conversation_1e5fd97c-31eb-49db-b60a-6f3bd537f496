
#ifndef FILE_H
#define FILE_H

#include "mycan.h"

#define MAX_SYNC_LEN 500

typedef enum
{
    ATWR = 0x0,    // 用于存日志文件，在后面追加
    WTWR = 0x01,   // 用于存储接收的文件，若有原文件清0
    ONLY_R = 0x02, // 用于读取要发送的文件

} enum_file_mode;

int Func_Dprintf(char *destpoint, char *fmt, ...);
void file_add_full_fath(char *dest, char *src, char *name);
FILE *file_open(char *path, enum_file_mode mode);
void file_write_data(FILE *pfile, char *scr_ata, int len);
void file_write_time_data(FILE *pfile, char *scr_ata, int len);
void file_write_fmt(FILE *pfile, char *fmt, ...);
void file_write_time_fmt(FILE *pfile, char *fmt, ...);
void file_write_head(FILE *pfile, struct_can_param param, char *can_dev);
int file_read(FILE *pfile, char *scr_ata, int len, long location);
void file_close(FILE *pfile);
int Func_Time_GetSystemTime_ToChar(char *TimeChar);

#endif