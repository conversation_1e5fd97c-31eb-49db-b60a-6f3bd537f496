#!/bin/bash

# 获取脚本所在目录的绝对路径
current_dir=$(cd "$(dirname "$0")" && pwd)

# 创建type_test子目录
dir_tmp=$current_dir/type_test
mkdir -p $dir_tmp
	
# 安装前可以
# 写一些 协议信息提示用户接收，如果用户不接收，安装退出；
# 还有写检测环境,版本之类的,卸载旧版本软件
	
echo -e "\t Prepare to install software v03.001..."
#----------
# 为打印除从第一行到所在exit 0的行的所有行到${dir_tmp}/target.tar.gz,如果过程中有错误则输出到/dev/null
# $0 代表脚本本身即从bin安装包中实现分离
# '1,/^exit 0$/!p' 代表从一行到以exit开头以0结尾的行(即shell脚本部分)
# ！代表取反，所以最后分离的是出了shell脚本的部分也即压缩包 
line=30
tail -n+$line $0 > ${dir_tmp}/target.tar 2>/dev/null
	
cd $dir_tmp
#将分离出的脚本解压缩到/tmp文件夹下
tar xvf target.tar -C ./
#解压完成后删除压缩包
rm -rf target.tar
	
exit 0
