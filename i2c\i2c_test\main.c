#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <sys/ioctl.h>
#include <sys/stat.h>
#include <linux/types.h>
#include <linux/i2c.h>
#include <linux/i2c-dev.h>
#include <signal.h>
#include "i2c.h"
#include "file.h"
#include "display.h"

char ver[20] = {"ver03.001"}; // 测试程序版本
char dev[20];                 // IIC设备名称
int iic_fd;

// 数据收发应用
unsigned int send_num = 0, receive_num = 0;

int active_send_mode = 0, active_send_num = 8, active_send_time = 2000;
unsigned long total_send = 0, total_receive = 0;
unsigned long total_send_group = 0, want_send_times = 0;
int err_count = 0;

// 存储日志
char history_log_path[10] = {"log"};
char temp_log_path[10] = {"temp"};
char history_log_name[30] = {0};
char temp_log_name[30] = {0};
char log_path[50] = {"/root/"};
FILE *pHistoryLogFile = NULL;
FILE *pTempLogFile = NULL;

struct_i2c_param i2c_param = {0x50, 1, 12, "test_EEP_IIC", 1, 12}; /*外设参数初始化*/

/*
 * @description : 自定义打印函数
 * @param - buff: 打印数据缓冲区
 * @param - lens: 打印数据长度
 * @param - mode: 打印格式
 * @return		: 无
 */
void func_my_print(char *buff, unsigned int lens, unsigned char mode, unsigned char save)
{
    int i = 0;
    switch (mode)
    {
    case 'H': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "0X%02X  ", buff[i]);
            else
                printf("0X%02X  ", buff[i]);
        }
        break;
    case 'h': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%02x  ", buff[i]);
            else
                printf("%02x  ", buff[i]);
        }
        break;
    case 'd': // 按照10进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%02d  ", buff[i]);
            else
                printf("%02d  ", buff[i]);
        }
        break;
    case 'c': // 按照字符打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%c", buff[i]);
            else
                printf("%c", buff[i]);
        }
        break;
    default:
        break;
    }

    if (!save)
        printf("\n");
    else
        file_write_fmt(pHistoryLogFile, "\n\n");
}

/*
 * @description  : 打印参数设置格式
 * @param - pname: 函数名
 * @return		 : 无
 */
static void print_usage(const char *pname)
{
    printf("Usage: %s device [-d dev_addr ] [-t time] [-c times] [-p path] [-v]"
           "\n\t'-d dev_addr' for device addr"
           "\n\t'-t time'  for interval set time actively sends data, unit is ms,time should not be less than 1"
           "\n\t'-c times'  for total send times"
           "\n\t'-p path' for log file path  example : /root/"
           "\n\t'-v' show version"
           "\n\texample :  --> ./i2c_test i2c-0 -d 80 -t 1000 -c 60 -p /root/ \n ",
           pname);
}

/*
 * @description   : 解析函数带入参数
 * @param - numb  : 参数个数
 * @param - *param: 带入参数数组指针
 * @param - *i2c_param: i2c应用参数
 * @param - *set  : i2c测试参数
 * @return		  : 无
 */
void get_param(int numb, char *param[], struct_i2c_param *i2cparam)
{
    int i = 0, len = 0;
    int temp = 0;

    if (numb <= 2)
        return;

    for (i = 2; i < numb; i++)
    {
        if (!strcmp(param[i], "-d"))
        {
            i++;

            temp = atoi(param[i]);
            if (temp > 0)
            {
                i2cparam->dev_addr = temp;
            }
            continue;
        }
        if (!strcmp(param[i], "-t"))
        {
            active_send_mode = 1;

            i++;
            len = atoi(param[i]); // 发送间隔
            if (len > 1)
            {
                active_send_time = len * 1000; // 转换为ms单位
            }
            else
            {
                printf("The sending interval cannot be less than 1.\n");
                print_usage(param[0]);
                exit(1);
            }
            continue;
        }
        if (!strcmp(param[i], "-c"))
        {
            i++;
            len = atoi(param[i]);
            if (len > 0)
            {
                want_send_times = len;
            }
            continue;
        }
        if (!strcmp(param[i], "-p"))
        {
            i++;
            strcpy(log_path, param[i]);
            continue;
        }
        if (!strcmp(param[i], "-v"))
        {
            printf("i2c_demo ver:  %s\n", ver);
            continue;
        }
    }
}

/*
 * @description    : 信号处理函数
 * @param - iSignNo: 信号
 * @return		   : 执行结果
 */
void SignHandler(int iSignNo)
{
    printf("total_send=%ld, total_receive=%ld \n", total_send, total_receive);
    file_write_time_fmt(pTempLogFile, "%s total_send=%ld, total_receive=%ld \n", dev, total_send, total_receive);
    if (err_count)
    {
        file_write_time_fmt(pTempLogFile, "%s test result is FAILED \n", dev);
        printf(L_RED "%s test result is FAILED" NONE "\n", dev);
    }
    else
    {
        file_write_time_fmt(pTempLogFile, "%s test result is PASSED \n", dev);
        printf(L_GREEN "%s test result is PASSED" NONE "\n", dev);
    }

    fflush(pHistoryLogFile);     /*将数据同步至ROM*/
    file_close(pHistoryLogFile); /*关闭文件*/

    fflush(pTempLogFile);     /*将数据同步至ROM*/
    file_close(pTempLogFile); /*关闭文件*/

    usleep(20000);

    exit(0);
}

/*
 * @description  : 主函数
 * @param - argc : 参数个数
 * @param - *argv: 带入参数数组指针
 * @return		 : 执行结果
 */
int main(int argc, char *argv[])
{
    int result = 0, i = 0;
    char history_log_full_name[100] = {0};
    char temp_log_full_name[100] = {0};

    // 检测是否有参数
    if (argc < 2 || strncmp(argv[1], "i2c", 3))
    {
        print_usage(argv[0]);
        exit(1);
    }

    // 检测是否有--h或--help
    if ((!strcmp(argv[1], "--h")) || (!strcmp(argv[1], "--help")))
    {
        print_usage(argv[0]);
        exit(1);
    }

    strcpy(dev, "/dev/");
    strcat(dev, argv[1]);

    // 从main函数带入的参数解析为i2c应用参数
    get_param(argc, argv, &i2c_param);

    // 准备历史日志文件
    file_add_full_fath(history_log_full_name, log_path, history_log_path);
    if ((access(history_log_full_name, 0)) == -1)
    {
        printf("not exist\n");
        mkdir(history_log_full_name, S_IRWXU);
    }
    sprintf(history_log_name, "/%s_history.log", argv[1]);
    strcat(history_log_full_name, history_log_name);
    pHistoryLogFile = file_open(history_log_full_name, ATWR);
    if (NULL == pHistoryLogFile)
        exit(1);
    // 准备临时日志文件
    file_add_full_fath(temp_log_full_name, log_path, temp_log_path);
    if ((access(temp_log_full_name, 0)) == -1)
    {
        printf("not exist\n");
        mkdir(temp_log_full_name, S_IRWXU);
    }
    sprintf(temp_log_name, "/%s_temp.log", argv[1]);
    strcat(temp_log_full_name, temp_log_name);
    pTempLogFile = file_open(temp_log_full_name, WTWR);
    if (NULL == pTempLogFile)
        exit(1);

    // 给历史文件和临时文件附测试头
    file_write_head(pHistoryLogFile, dev);
    file_write_head(pTempLogFile, dev);
    file_write_time_fmt(pHistoryLogFile, "[%s nwrite=%d]  ", dev, i2c_param.w_len);
    func_my_print(i2c_param.w_data, i2c_param.w_len, 'c', 1); // 将收到的数据打印出来

    // 进程被kill 需要保存文件
    signal(SIGTERM, SignHandler);
    // 按下 ctrl + c 需要保存文件
    signal(SIGINT, SignHandler);

    // 打开i2c 外设
    iic_fd = open(dev, O_RDWR);
    if (iic_fd < 0)
    {
        perror(dev);
        printf("Can't Open i2c device %s \n", dev);
        exit(0);
    }
    else
    {
        // 打印参数
        printf("dev_addr=0x%x \n", i2c_param.dev_addr);
        // 设置i2c参数
        if ((result = func_set_opt(iic_fd)) < 0)
        {
            perror("set_opt error");
            exit(0);
        }

        // 设置i2c为非阻塞方式
        if (fcntl(iic_fd, F_SETFL, FNDELAY) < 0)
        {
            printf("fcntl failed!\n");
        }
    }

    while (1)
    {
        memset(i2c_param.r_data, 0, MAX_LEN);

        for (i = 0; i < i2c_param.w_len; i++)
        {
            func_write_regs(iic_fd, i2c_param.dev_addr, i2c_param.w_addr++, &i2c_param.w_data[i], 1);
            usleep(3000);
        }
        total_send_group++;
        // file_write_time_fmt(pHistoryLogFile, "[%s nwrite=%d]  ", dev, i2c_param.w_len);
        // func_my_print(i2c_param.w_data, i2c_param.w_len, 'c', 1); // 将收到的数据打印出来

        for (i = 0; i < i2c_param.r_len; i++)
        {
            func_read_regs(iic_fd, i2c_param.dev_addr, i2c_param.r_addr++, &i2c_param.r_data[i], 1);
            usleep(1000);
        }
        // file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, i2c_param.r_len);
        // func_my_print(i2c_param.r_data, i2c_param.r_len, 'c', 1); // 将收到的数据打印出来
        total_send += i2c_param.w_len;
        total_receive += i2c_param.r_len;

        if (0 != strncmp((const char *)i2c_param.w_data, (const char *)i2c_param.r_data, i2c_param.w_len))
        {
            printf(L_RED "%s data receive and send diffrent.!!!" NONE "\n", dev);
            file_write_time_fmt(pHistoryLogFile, "%s data receive and send diffrent.!!!\n", dev);
            file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, i2c_param.r_len);
            func_my_print(i2c_param.r_data, i2c_param.r_len, 'c', 1); // 将收到的数据打印出来
            err_count++;
        }
        if (want_send_times)
        {
            if (total_send_group >= want_send_times)
            {
                printf("total_send=%ld, total_receive=%ld \n", total_send, total_receive);
                file_write_time_fmt(pTempLogFile, "%s total_send=%ld, total_receive=%ld \n", dev, total_send, total_receive);
                if (err_count)
                {
                    file_write_time_fmt(pTempLogFile, "%s test result is FAILED \n", dev);
                    printf(L_RED "%s test result is FAILED" NONE "\n", dev);
                }
                else
                {
                    file_write_time_fmt(pTempLogFile, "%s test result is PASSED \n", dev);
                    printf(L_GREEN "%s test result is PASSED" NONE "\n", dev);
                }
                fflush(pHistoryLogFile);     /*将数据同步至ROM*/
                file_close(pHistoryLogFile); /*关闭文件*/

                fflush(pTempLogFile);     /*将数据同步至ROM*/
                file_close(pTempLogFile); /*关闭文件*/

                close(iic_fd);
                exit(0);
            }

            usleep(active_send_time);
        }
        exit(1);
    }
}
