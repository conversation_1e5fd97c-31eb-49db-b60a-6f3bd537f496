#3568_4_19_206
echo "begin 3568_4_19_206 cross compile"
export PATH=$PATH:/home/<USER>/3568_4_19_206/prebuilts/gcc/linux-x86/aarch64/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin
export CC="/home/<USER>/3568_4_19_206/prebuilts/gcc/linux-x86/aarch64/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc --sysroot=/home/<USER>/3568_4_19_206/prebuilts/gcc/linux-x86/aarch64/gcc-buildroot-9.3.0-2020.03-x86_64_aarch64-rockchip-linux-gnu/aarch64-rockchip-linux-gnu/sysroot"
export AR="/home/<USER>/3568_4_19_206/prebuilts/gcc/linux-x86/aarch64/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-ar"

#3568_5_10_160
. /home/<USER>/3568_5_10_160/host/environment-setup

#3399_4_4_189
export PATH=$PATH:/home/<USER>/3399_4_4_189/prebuilts/gcc/linux-x86/aarch64/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin
export CC="/home/<USER>/3399_4_4_189/prebuilts/gcc/linux-x86/aarch64/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc --sysroot=/home/<USER>/3399_4_4_189/buildroot/output/rockchip_rk3399/host/aarch64-buildroot-linux-gnu/sysroot"
export AR="/home/<USER>/3399_4_4_189/prebuilts/gcc/linux-x86/aarch64/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-ar"

#3588_5_10_66
export PATH=$PATH:/home/<USER>/3588_5_10_66/aarch64-buildroot-linux-gnu_sdk-buildroot/usr/bin
export CC="/home/<USER>/3588_5_10_66/aarch64-buildroot-linux-gnu_sdk-buildroot/usr/bin/aarch64-buildroot-linux-gnu-gcc --sysroot=/home/<USER>/3588_5_10_66/aarch64-buildroot-linux-gnu_sdk-buildroot/aarch64-buildroot-linux-gnu/sysroot"
export AR="/home/<USER>/3588_5_10_66/aarch64-buildroot-linux-gnu_sdk-buildroot/usr/bin/aarch64-buildroot-linux-gnu-ar"

#3588_5_10_209
export PATH=$PATH:/home/<USER>/3588_5_10_209/aarch64-buildroot-linux-gnu_sdk-buildroot/usr/bin
export CC="/home/<USER>/3588_5_10_209/aarch64-buildroot-linux-gnu_sdk-buildroot/usr/bin/aarch64-buildroot-linux-gnu-gcc --sysroot=/home/<USER>/3588_5_10_209/aarch64-buildroot-linux-gnu_sdk-buildroot/aarch64-buildroot-linux-gnu/sysroot"
export AR="/home/<USER>/3588_5_10_209/aarch64-buildroot-linux-gnu_sdk-buildroot/usr/bin/aarch64-buildroot-linux-gnu-ar"

#3576_6_1_57
export PATH=$PATH:/home/<USER>/3576_6_1_57/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin
export CC="/home/<USER>/3576_6_1_57/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc --sysroot=/home/<USER>/3576_6_1_57/host/aarch64-buildroot-linux-gnu/sysroot"
export AR="/home/<USER>/3576_6_1_57/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-ar"

#3562_5_10_198
. /home/<USER>/3562_5_10_198/aarch64-buildroot-linux-gnu_sdk-buildroot/environment-setup

#6ulx_4_1_15
export PATH=$PATH:/home/<USER>/6ulx_4_1_15/fsl-imx-x11/4.1.15-2.0.0/sysroots/x86_64-pokysdk-linux/usr/bin/arm-poky-linux-gnueabi
. /home/<USER>/6ulx_4_1_15/fsl-imx-x11/4.1.15-2.0.0/environment-setup-cortexa7hf-neon-poky-linux-gnueabi

#6q_4_1_15
export PATH=$PATH:/home/<USER>/6q_4_1_15/sysroots/x86_64-pokysdk-linux/usr/bin/arm-poky-linux-gnueabi
 . /home/<USER>/6q_4_1_15/environment-setup-cortexa9hf-neon-poky-linux-gnueabi
  
#6q_5_10_72
export PATH=$PATH:/home/<USER>/6q_5_10_72/sysroots/x86_64-pokysdk-linux/usr/bin/arm-poky-linux-gnueabi
. /home/<USER>/6q_5_10_72/environment-setup-cortexa9t2hf-neon-poky-linux-gnueabi

#6q_6_6_52
export PATH=$PATH:/home/<USER>/6q_6_6_52/toolchain/sysroots/x86_64-pokysdk-linux/usr/bin/arm-poky-linux-gnueabi
. /home/<USER>/6q_6_6_52/toolchain/environment-setup-cortexa9t2hf-neon-poky-linux-gnueabi

#8mp_5_4_3
. /home/<USER>/8mpq_5_4_3/fsl-imx-xwayland/5.4-zeus/environment-setup-aarch64-poky-linux

#8mm_4_14_78
. /home/<USER>/8mm_4_14_78/environment-setup-aarch64-poky-linux

#8mq_5_4_3
. /home/<USER>/8mq_5_4_3/environment-setup-aarch64-poky-linux

#93x_5_15_52
. /home/<USER>/93x_5_15_52/fsl-imx-xwayland/5.15-kirkstone/environment-setup-armv8a-poky-linux

#93x_6_1
. /home/<USER>/93x_6_1/environment-setup-armv8a-poky-linux

#g2l_4_19_165
. /home/<USER>/g2l_4_19_165/poky/3.1.5/environment-setup-aarch64-poky-linux
. /home/<USER>/g2l_4_19_165/poky/3.1.5/environment-setup-armv7vet2hf-neon-vfpv4-pokymllib32-linux-gnueabi

#g2l_5_10_158
. /home/<USER>/g2l_5_10_158/poky/3.1.21/environment-setup-aarch64-poky-linux
. /home/<USER>/g2l_5_10_158/poky/3.1.21/environment-setup-armv7vet2hf-neon-vfpv4-pokymllib32-linux-gnueabi

#10xx
export CC="/usr/bin/aarch64-linux-gnu-gcc"
export AR="/usr/bin/aarch64-linux-gnu-ar"

#T507_4_9 
export PATH=/home/<USER>/t507_4_9/Qt5.12/gcc-linaro-7.4.1-2019.02-x86_64_aarch64-linux-gnu/bin:$PATH
export CC="/home/<USER>/t507_4_9/Qt5.12/gcc-linaro-7.4.1-2019.02-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc  --sysroot=/home/<USER>/t507_4_9/Qt5.12/host/aarch64-buildroot-linux-gnu/sysroot"
export AR="/home/<USER>/t507_4_9/Qt5.12/gcc-linaro-7.4.1-2019.02-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc-ar"

#T113_5_4_61
export PATH=/home/<USER>/t113_5_4_61/host/opt/ext-toolchain/bin:$PATH
export CC="/home/<USER>/t113_5_4_61/host/opt/ext-toolchain/bin/arm-linux-gnueabi-gcc-7.3.1 --sysroot=/home/<USER>/t113_5_4_61/host/arm-buildroot-linux-gnueabi/sysroot"
export AR="/home/<USER>/t113_5_4_61/host/opt/ext-toolchain/bin/arm-linux-gnueabi-ar"

#D9 
export PATH=/home/<USER>/d9_4_14_61/gcc_linaro/gcc-linaro-7.3.1-2018.05-x86_64_aarch64-linux-gnu/bin:$PATH
export CC="/home/<USER>/d9_4_14_61/gcc_linaro/gcc-linaro-7.3.1-2018.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc --sysroot=/home/<USER>/d9_4_14_61/host/aarch64-buildroot-linux-gnu/sysroot"
export AR="/home/<USER>/d9_4_14_61/gcc_linaro/gcc-linaro-7.3.1-2018.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-ar"

#MA35 
export PATH=/home/<USER>/MA35_5_10_140/host/aarch64-nuvoton-linux-gnu/bin:$PATH
export CC="/home/<USER>/MA35_5_10_140/host/bin/aarch64-nuvoton-linux-gnu-gcc --sysroot=/home/<USER>/MA35_5_10_140/host/aarch64-nuvoton-linux-gnu/sysroot"
export AR="/home/<USER>/MA35_5_10_140/host/bin/aarch64-nuvoton-linux-gnu-ar"

#xx18 
export PATH=/home/<USER>/xx18/arm-cortex_a9-eabi-4.7-eglibc-2.18/bin:$PATH
export CC="/home/<USER>/xx18/arm-cortex_a9-eabi-4.7-eglibc-2.18/bin/arm-cortex_a9-linux-gnueabi-gcc --sysroot=/home/<USER>/xx18/arm-cortex_a9-eabi-4.7-eglibc-2.18/arm-cortex_a9-linux-gnueabi/sysroot"
export AR="/home/<USER>/xx18/arm-cortex_a9-eabi-4.7-eglibc-2.18/bin/arm-cortex_a9-linux-gnueabi-ar"

#A40I_3_10
export PATH=/home/<USER>/A40i_3_10/arm/opt/ext-toolchain/bin:$PATH
export CC="/home/<USER>/A40i_3_10/arm/opt/ext-toolchain/bin/arm-linux-gnueabihf-gcc --sysroot=/home/<USER>/A40i_3_10/arm/usr/arm-buildroot-linux-gnueabihf/sysroot"
export AR="/home/<USER>/A40i_3_10/arm/opt/ext-toolchain/bin/arm-linux-gnueabihf-ar"

#A40I_5_10
export PATH=/home/<USER>/A40i_5_10/host/bin:$PATH
export CC="/home/<USER>/A40i_5_10/host/bin/arm-linux-gnueabihf-gcc --sysroot=/home/<USER>/A40i_5_10/host/arm-buildroot-linux-gnueabihf/sysroot"
export AR="/home/<USER>/A40i_5_10/host/bin/arm-linux-gnueabihf-ar"

#T536_5_10
export PATH=/home/<USER>/t536_5_10/host/bin:$PATH
export CC="/home/<USER>/t536_5_10/host/bin/aarch64-none-linux-gnu-gcc --sysroot=/home/<USER>/t536_5_10/host/aarch64-buildroot-linux-gnu/sysroot"
export AR="/home/<USER>/t536_5_10/host/bin/aarch64-none-linux-gnu-ar"

#T3_3_10
export PATH=/home/<USER>/T3_3_10/arm/opt/ext-toolchain/bin:$PATH
export CC="/home/<USER>/T3_3_10/arm/opt/ext-toolchain/bin/arm-linux-gnueabihf-gcc --sysroot=/home/<USER>/T3_3_10/arm/usr/arm-buildroot-linux-gnueabihf/sysroot"
export AR="/home/<USER>/T3_3_10/arm/opt/ext-toolchain/bin/arm-linux-gnueabihf-ar"

#6254_5_10_87
. /home/<USER>/6254_5_10_87/arago-2021.09/environment-setup

#6254_6_1_33
. /home/<USER>/6254_6_1_33/arago-2023.04/environment-setup

#T527_5_15_104
export PATH=/home/<USER>/T527_5_15_104/aarch64-buildroot-linux-gnu_sdk-buildroot/bin:$PATH
export CC="/home/<USER>/T527_5_15_104/aarch64-buildroot-linux-gnu_sdk-buildroot/bin/aarch64-none-linux-gnu-gcc  --sysroot=/home/<USER>/T527_5_15_104/aarch64-buildroot-linux-gnu_sdk-buildroot/aarch64-buildroot-linux-gnu/sysroot"
export AR="/home/<USER>/T527_5_15_104/aarch64-buildroot-linux-gnu_sdk-buildroot/bin/aarch64-none-linux-gnu-ar"

#LOONG_4_19_190
export PATH=$PATH:/home/<USER>/loongson_4_19_190_r14/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.4/loongarch64-linux-gnu/bin
export CC="/home/<USER>/loongson_4_19_190_r14/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.4/bin/loongarch64-linux-gnu-gcc --sysroot=/home/<USER>/loongson_4_19_190_r14/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.4/loongarch64-linux-gnu/sysroot"
export AR="/home/<USER>/loongson_4_19_190_r14/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.4/bin/loongarch64-linux-gnu-ar"

#classify
ALLWINNER = [T,A40]
NXP       = [6u,6q,6x,8m,93,10]
RK        = [33,35]
LOONGSON  = [loo]
NUVOTON = [MA]
RENESAS = [g2]
SEMDRVE = [D9]
TI      = [62]
