/*
 *  nanddump.c
 *
 *  Copyright (C) 2000 <PERSON> (<EMAIL>)
 *                     <PERSON> (<EMAIL>)
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 *  Overview:
 *   This utility dumps the contents of raw NAND chips or NAND
 *   chips contained in DoC devices.
 */

#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <getopt.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/stat.h>

#include <asm/types.h>
#include <mtd/mtd-user.h>
#include "libmtd.h"
#include "libmtd_int.h"

#define PRETTY_ROW_SIZE 16
#define PRETTY_BUF_LEN 80

static bool omitoob = true; // omit oob data

/*
 * Main program
 */
int main(int argc, char *const argv[])
{
	long long ofs, end_addr = 0, start_addr = 0;
	long long blockstart = 1;
	int fd, ofd = 0, bs, badblock = 0;
	struct mtd_dev_info mtd;
	int firstblock = 1;
	struct mtd_ecc_stats stat1, stat2;
	bool eccstats = false;
	unsigned char *readbuf = NULL, *oobbuf = NULL;
	libmtd_t mtd_desc;

	/* Initialize libmtd */
	mtd_desc = libmtd_open();
	if (!mtd_desc)
		return errmsg("can't initialize libmtd");

	/* Open MTD device */
	if ((fd = open(argv[1], O_RDONLY)) == -1)
	{
		perror(argv[1]);
		exit(EXIT_FAILURE);
	}

	/* Fill in MTD device capability structure */
	if (mtd_get_dev_info(mtd_desc, argv[1], &mtd) < 0)
		return errmsg("mtd_get_dev_info failed");

	/* Allocate buffers */
	oobbuf = xmalloc(sizeof(oobbuf) * mtd.oob_size);
	readbuf = xmalloc(sizeof(readbuf) * mtd.min_io_size);

	/* check if we can read ecc stats */
	if (!ioctl(fd, ECCGETSTATS, &stat1))
	{
		eccstats = true;
		// fprintf(stderr, "ECC failed: %d\n", stat1.failed);
		// fprintf(stderr, "ECC corrected: %d\n", stat1.corrected);
		// fprintf(stderr, "Number of bad blocks: %d\n", stat1.badblocks);
		// fprintf(stderr, "Number of bbt blocks: %d\n", stat1.bbtblocks);
	}
	else
		perror("No ECC status information available");

	/* Open output file for writing. If file name is "-", write to standard
	 * output. */
	if ((ofd = open("/dev/null", O_WRONLY | O_TRUNC | O_CREAT, 0644)) == -1)
	{
		perror("/dev/null");
		goto closeall;
	}

	end_addr = mtd.size;

	bs = mtd.min_io_size;

	/* Print informative message */
	fprintf(stderr, "Block size %d, page size %d, OOB size %d\n",
			mtd.eb_size, mtd.min_io_size, mtd.oob_size);
	fprintf(stderr,
			"Dumping data starting at 0x%08llx and ending at 0x%08llx...\n",
			start_addr, end_addr);

	/* Dump the flash contents */
	for (ofs = start_addr; ofs < end_addr; ofs += bs)
	{
		if (blockstart != (ofs & (~mtd.eb_size + 1)) ||
			firstblock)
		{
			blockstart = ofs & (~mtd.eb_size + 1);
			firstblock = 0;
			if ((badblock = mtd_is_bad(&mtd, fd, ofs / mtd.eb_size)) < 0)
			{
				errmsg("libmtd: mtd_is_bad");
				goto closeall;
			}
		}

		if (badblock)
		{
			/* skip bad block, increase end_addr */
			end_addr += mtd.eb_size;
			ofs += mtd.eb_size - bs;
			if (end_addr > mtd.size)
				end_addr = mtd.size;
			continue;
			memset(readbuf, 0xff, bs);
		}
		else
		{
			/* Read page data and exit on failure */
			if (mtd_read(&mtd, fd, ofs / mtd.eb_size, ofs % mtd.eb_size, readbuf, bs))
			{
				errmsg("mtd_read");
				goto closeall;
			}
		}

		/* ECC stats available ? */
		if (eccstats)
		{
			if (ioctl(fd, ECCGETSTATS, &stat2))
			{
				perror("ioctl(ECCGETSTATS)");
				goto closeall;
			}
			if (stat1.failed != stat2.failed)
				fprintf(stderr, "ECC: %d uncorrectable bitflip(s)"
								" at offset 0x%08llx\n",
						stat2.failed - stat1.failed, ofs);
			if (stat1.corrected != stat2.corrected)
				fprintf(stderr, "ECC: %d corrected bitflip(s) at"
								" offset 0x%08llx\n",
						stat2.corrected - stat1.corrected, ofs);
			stat1 = stat2;
		}

		write(ofd, readbuf, bs);

		if (omitoob)
			continue;

		if (badblock)
		{
			memset(oobbuf, 0xff, mtd.oob_size);
		}
		else
		{
			/* Read OOB data and exit on failure */
			if (mtd_read_oob(mtd_desc, &mtd, fd, ofs, mtd.oob_size, oobbuf))
			{
				// errmsg("libmtd: mtd_read_oob");
				goto closeall;
			}
		}

		write(ofd, oobbuf, mtd.oob_size);
	}

	/* Close the output file and MTD device, free memory */
	fprintf(stderr, "ECC failed: %d\n", stat1.failed);
	fprintf(stderr, "ECC corrected: %d\n", stat1.corrected);
	fprintf(stderr, "Number of bad blocks: %d\n", stat1.badblocks);
	fprintf(stderr, "Number of bbt blocks: %d\n", stat1.bbtblocks);

	close(fd);
	close(ofd);
	free(oobbuf);
	free(readbuf);

	/* Exit happy */
	return EXIT_SUCCESS;

closeall:
	close(fd);
	close(ofd);
	free(oobbuf);
	free(readbuf);
	exit(EXIT_FAILURE);
}
