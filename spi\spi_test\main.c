#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <sys/stat.h>
#include <linux/types.h>
#include <signal.h>
#include "spi.h"
#include "display.h"
#include "file.h"

char ver[20] = {"ver03.001"};
char dev[20];
int spi_fd;

struct_spi_param spi_param = {1000000, 0, 8, 0, 0, 0, 0, 0, 0, 0}; /*外设参数初始化*/
// 数据收发应用
unsigned char send_buff[MAX_BUF_LEN], receive_buff[MAX_BUF_LEN];
unsigned int send_num = 0, receive_num = 0;

int active_send_mode = 0, active_send_num = 8, active_send_time = 2000;
unsigned long total_send = 0, total_receive = 0;
unsigned long total_send_group = 0, want_send_times = 0;
int err_count = 0;

// 存储日志
char history_log_path[10] = {"log"};
char temp_log_path[10] = {"temp"};
char history_log_name[30] = {0};
char temp_log_name[30] = {0};
char log_path[50] = {"/root/"};
FILE *pHistoryLogFile = NULL;
FILE *pTempLogFile = NULL;

/*
 * @description : 自定义打印函数
 * @param - buff: 打印数据缓冲区
 * @param - lens: 打印数据长度
 * @param - mode: 打印格式
 * @return		: 无
 */
void func_my_print(unsigned char *buff, unsigned int lens, unsigned char mode, unsigned char save)
{
    int i = 0;

    switch (mode)
    {
    case 'H': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "0X%02X  ", buff[i]);
            else
                printf("0X%02X  ", buff[i]);
        }
        break;
    case 'h': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%02x  ", buff[i]);
            else
                printf("%02x  ", buff[i]);
        }
        break;
    case 'd': // 按照10进制打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%02d  ", buff[i]);
            else
                printf("%02d  ", buff[i]);
        }
        break;
    case 'c': // 按照字符打印数据
        for (i = 0; i < lens; i++)
        {
            if (save)
                file_write_fmt(pHistoryLogFile, "%c", buff[i]);
            else
                printf("%c", buff[i]);
        }
        break;
    default:
        break;
    }
    if (!save)
        printf("\n");
    else
        file_write_fmt(pHistoryLogFile, "\n\n");
}

/*
 * @description : 打印参数设置格式
 * @param - pname: 函数名
 * @return		: 无
 */
static void print_usage(const char *pname)
{
    printf("Usage: %s device [-s speed] [-d delay] [-m mode] [-t len time] [-c times] [-p path] [-v]"
           "\n\t'-s speed' for max speed (kHz)"
           "\n\t'-d delay' for delay (usec)"
           "\n\t'-m mode' for 0:CPOL=0,CPHA=0  1:CPOL=0,CPHA=1 2:CPOL=1,CPHA=0 3:CPOL=1,CPHA=1 "
           "\n\t'-t len time'  for interval set time actively sends the len data, unit is ms,time should not be less than 1"
           "\n\t'-c times'  for total send times"
           "\n\t'-p path' for log file path  example : /root/"
           "\n\t'-v' show version"
           "\n\texample :  --> ./spi_test spidev1.0 -t 500 1000 -c 60 -p /root/ \n ",
           pname);
}

/*
 * @description : 解析函数带入参数
 * @param - numb: 参数个数
 * @param - *param: 带入参数数组指针
 * @param - *spiparam: spi应用参数
 * @return		: 无
 */
void get_param(int numb, char *param[], struct_spi_param *spiparam)
{
    int i = 0, len = 0, j = 0;
    unsigned char data = 0;
    unsigned int speed = 0;

    if (numb <= 2)
        return;

    for (i = 2; i < numb; i++)
    {
        if (!strcmp(param[i], "-s"))
        {
            i++;
            speed = atoi(param[i]);
            spiparam->speed = speed * 1000;
            continue;
        }
        if (!strcmp(param[i], "-d"))
        {
            i++;
            data = atoi(param[i]);
            spiparam->delay = data;
            continue;
        }
        if (!strcmp(param[i], "-b"))
        {
            i++;
            data = atoi(param[i]);
            if (16 == data)
            {
                spiparam->data_bit = data;
            }
            else
            {
                spiparam->data_bit = 8;
            }
            continue;
        }
        if (!strcmp(param[i], "-m"))
        {
            i++;
            data = atoi(param[i]);
            switch (data)
            {
            case 0:
            case 1:
            case 2:
            case 3:
                spiparam->mode = data;
                break;
            default:
                spiparam->mode = 0;
                break;
            }
            continue;
        }
        if (!strcmp(param[i], "-t"))
        {
            active_send_mode = 1;

            i++;
            len = atoi(param[i]);
            if (len > 0)
            {
                active_send_num = len;
                for (j = 0; j < active_send_num; j++)
                {
                    send_buff[j] = j;
                    if (!send_buff[j])
                        send_buff[j] = 1;
                }

                i++;
                len = atoi(param[i]); // 发送间隔
                if (len > 1)
                {
                    active_send_time = len * 1000; // 转换为ms单位
                }
                else
                {
                    printf("The sending interval cannot be less than 1.\n");
                    print_usage(param[0]);
                    exit(1);
                }
            }
            continue;
        }
        if (!strcmp(param[i], "-c"))
        {
            i++;
            len = atoi(param[i]);
            if (len > 0)
            {
                want_send_times = len;
            }
            continue;
        }
        if (!strcmp(param[i], "-p"))
        {
            i++;
            strcpy(log_path, param[i]);
            continue;
        }
        if (!strcmp(param[i], "-v"))
        {
            printf("spi_demo ver:  %s\n", ver);
            continue;
        }
    }
}

/*
 * @description    : 信号处理函数
 * @param - iSignNo: 信号
 * @return		   : 执行结果
 */
void SignHandler(int iSignNo)
{
    printf("total_send=%ld, total_receive=%ld \n", total_send, total_receive);
    file_write_time_fmt(pTempLogFile, "%s total_send=%ld, total_receive=%ld \n", dev, total_send, total_receive);
    if (err_count)
    {
        file_write_time_fmt(pTempLogFile, "%s test result is FAILED \n", dev);
        printf(L_RED "%s test result is FAILED" NONE "\n", dev);
    }
    else
    {
        file_write_time_fmt(pTempLogFile, "%s test result is PASSED \n", dev);
        printf(L_GREEN "%s test result is PASSED" NONE "\n", dev);
    }

    fflush(pHistoryLogFile);     /*将数据同步至ROM*/
    file_close(pHistoryLogFile); /*关闭文件*/

    fflush(pTempLogFile);     /*将数据同步至ROM*/
    file_close(pTempLogFile); /*关闭文件*/

    usleep(20000);

    exit(0);
}

/*
 * @description  : 主函数
 * @param - argc : 参数个数
 * @param - *argv: 带入参数数组指针
 * @return		 : 执行结果
 */
int main(int argc, char *argv[])
{
    int result = 0;
    char history_log_full_name[100] = {0};
    char temp_log_full_name[100] = {0};

    // 检测是否有参数
    if (argc < 2 || strncmp(argv[1], "spi", 3))
    {
        print_usage(argv[0]);
        exit(1);
    }

    // 检测是否有--h或--help
    if ((!strcmp(argv[1], "--h")) || (!strcmp(argv[1], "--help")))
    {
        print_usage(argv[0]);
        exit(1);
    }

    strcpy(dev, "/dev/");
    strcat(dev, argv[1]);

    // 从main函数带入的参数解析为SPI应用参数
    get_param(argc, argv, &spi_param);

    // 准备历史日志文件
    file_add_full_fath(history_log_full_name, log_path, history_log_path);
    if ((access(history_log_full_name, 0)) == -1)
    {
        printf("not exist\n");
        mkdir(history_log_full_name, S_IRWXU);
    }
    sprintf(history_log_name, "/%s_history.log", argv[1]);
    strcat(history_log_full_name, history_log_name);
    pHistoryLogFile = file_open(history_log_full_name, ATWR);
    if (NULL == pHistoryLogFile)
        exit(1);
    // 准备临时日志文件
    file_add_full_fath(temp_log_full_name, log_path, temp_log_path);
    if ((access(temp_log_full_name, 0)) == -1)
    {
        printf("not exist\n");
        mkdir(temp_log_full_name, S_IRWXU);
    }
    sprintf(temp_log_name, "/%s_temp.log", argv[1]);
    strcat(temp_log_full_name, temp_log_name);
    pTempLogFile = file_open(temp_log_full_name, WTWR);
    if (NULL == pTempLogFile)
        exit(1);

    // 给历史文件和临时文件附测试头
    file_write_head(pHistoryLogFile, spi_param, dev);
    file_write_head(pTempLogFile, spi_param, dev);
    file_write_time_fmt(pHistoryLogFile, "[%s nwrite=%d]  ", dev, active_send_num);
    func_my_print(send_buff, active_send_num, 'h', 1); // 将收到的数据打印出来

    // 进程被kill 需要保存文件
    signal(SIGTERM, SignHandler);
    // 按下 ctrl + c 需要保存文件
    signal(SIGINT, SignHandler);

    // 打开SPI 外设
    spi_fd = open(dev, O_RDWR);
    if (spi_fd < 0)
    {
        perror(dev);
        printf("Can't Open spi device %s \n", dev);
        exit(1);
    }
    else
    {
        // 打印参数
        printf("%s peed=%ldhz,delay=%d,data_bit=%d,mode=%d,lsb=%d,no_cs='%d,loop mode=%d\n", dev, spi_param.speed, spi_param.delay, spi_param.data_bit,
               spi_param.mode, spi_param.lsb_first, spi_param.no_cs, spi_param.loop);
        // 设置SPI参数
        if ((result = func_set_opt(spi_fd, spi_param)) < 0)
        {
            perror("set_opt error");
            exit(0);
        }

        // 设置SPI为非阻塞方式
        if (fcntl(spi_fd, F_SETFL, FNDELAY) < 0)
        {
            printf("fcntl failed!\n");
        }
    }

    while (1)
    {
        memset(receive_buff, 0, active_send_num);
        // 主动发送数据
        func_transfer(spi_fd, send_buff, receive_buff, active_send_num, spi_param);

        total_send_group++;
        // file_write_time_fmt(pHistoryLogFile, "[%s nwrite=%d]  ", dev, active_send_num);
        // func_my_print(send_buff, active_send_num, 'h', 1); // 将收到的数据打印出来
        usleep(500000);
        // file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, active_send_num);
        // func_my_print(receive_buff, active_send_num, 'h', 1); // 将收到的数据打印出来
        total_send += active_send_num;
        total_receive += active_send_num;

        if (0 != strncmp((const char *)receive_buff, (const char *)send_buff, active_send_num))
        {
            printf(L_RED "%s data receive and send diffrent.!!!" NONE "\n", dev);
            file_write_time_fmt(pHistoryLogFile, "%s data receive and send diffrent.!!!\n", dev);
            err_count++;
            file_write_time_fmt(pHistoryLogFile, "[%s nread=%d]  ", dev, active_send_num);
            func_my_print(receive_buff, active_send_num, 'h', 1); // 将收到的数据打印出来
        }
        if (want_send_times)
        {
            if (total_send_group >= want_send_times)
            {
                printf("total_send=%ld, total_receive=%ld \n", total_send, total_receive);
                file_write_time_fmt(pTempLogFile, "%s total_send=%ld, total_receive=%ld \n", dev, total_send, total_receive);
                if (err_count)
                {
                    file_write_time_fmt(pTempLogFile, "%s test result is FAILED \n", dev);
                    printf(L_RED "%s test result is FAILED" NONE "\n", dev);
                }
                else
                {
                    file_write_time_fmt(pTempLogFile, "%s test result is PASSED \n", dev);
                    printf(L_GREEN "%s test result is PASSED" NONE "\n", dev);
                }
                fflush(pHistoryLogFile);     /*将数据同步至ROM*/
                file_close(pHistoryLogFile); /*关闭文件*/

                fflush(pTempLogFile);     /*将数据同步至ROM*/
                file_close(pTempLogFile); /*关闭文件*/

                close(spi_fd);
                exit(0);
            }
        }

        usleep(active_send_time);
    }
    exit(1);
}
