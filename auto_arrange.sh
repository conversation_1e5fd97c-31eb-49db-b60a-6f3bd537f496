#!/bin/bash
# 检查命令行参数
if [[ "$1" != "ALL" && "$1" != "PART" ]]; then
    echo "Usage: $0 ALL | PART"
    exit 1
fi

# 初始化变量
declare -A gcc_map
key_order=()

# 在脚本开始处添加日志文件路径
LOG_FILE="compile_result.log"
# 清空日志文件
> "$LOG_FILE"

# Parse gcc_all.txt file
while IFS= read -r line || [ -n "$line" ]; do
    # Skip if line starts with "#classify"
    if [[ "$line" =~ ^#classify ]]; then
        break  # Exit the loop when reaching classify section
    fi

    # 跳过空行，同时保存当前的键值对
    if [[ -z "$line" ]]; then
        if [[ -n "$current_key" && -n "$current_value" ]]; then
            gcc_map["$current_key"]="$current_value"
            current_key=""
            current_value=""
        fi
        continue
    fi

    # 如果行以#开头，表示这是一个新的键
    if [[ "$line" =~ ^#(.+) ]]; then
        # 保存之前的键值对（如果存在）
        if [[ -n "$current_key" && -n "$current_value" ]]; then
            gcc_map["$current_key"]="$current_value"
            current_value=""
        fi
        # 提取键（#后面到第一个空格之前的内容）
        current_key=$(echo "${BASH_REMATCH[1]}" | cut -d' ' -f1)
        # 将新的键添加到顺序数组中
        key_order+=("$current_key")
    else
        # 将行添加到当前值中，保持换行符
        if [[ -n "$current_value" ]]; then
            current_value+=$'\n'
        fi
        current_value+="$line"
    fi
done < gcc_all.txt

# 保存最后一个键值对（如果存在）
if [[ -n "$current_key" && -n "$current_value" ]]; then
    gcc_map["$current_key"]="$current_value"
fi

# 根据模式设置要处理的键列表
if [[ "$1" == "ALL" ]]; then
    # ALL模式：处理所有键
    keys_to_process=("${key_order[@]}")
elif [[ "$1" == "PART" ]]; then
    # PART模式：只处理指定的键
    # 在脚本中直接定义PART列表，用空格分开
    keys_to_process=("3568_4_19_206" "3568_5_10_160" "3399_4_4_189")
fi

# 获取平台对应的基础输出目录
get_output_base_dir() {
    local name=$1
    # RK platforms
    if [[ "$name" =~ ^(33|35) ]]; then
        echo "RK"
    # NXP platforms
    elif [[ "$name" =~ ^(6u|6q|6x|8m|93|10) ]]; then
        echo "NXP"
    # ALLWINNER platforms
    elif [[ "$name" =~ ^(T|A40) ]]; then
        echo "ALLWINNER"
    # LOONGSON platforms
    elif [[ "$name" =~ ^(loo) ]]; then
        echo "LOONGSON"
    # NUVOTON platforms
    elif [[ "$name" =~ ^(MA) ]]; then
        echo "NUVOTON"
    # RENESAS platforms
    elif [[ "$name" =~ ^(g2) ]]; then
        echo "RENESAS"
    # SEMDRVE platforms
    elif [[ "$name" =~ ^(D9) ]]; then
        echo "SEMDRVE"
    # TI platforms
    elif [[ "$name" =~ ^(62) ]]; then
        echo "TI"
    # Other platforms
    else
        echo "other"
    fi
}

# 编译所有版本
echo "开始编译所有版本..."
for key in "${keys_to_process[@]}"; do
    echo "----------------------------------------"
    echo "正在编译 $key 版本..."
    echo "设置环境变量..."

    # 确定输出目录
    base_dir=$(get_output_base_dir "$key")
    output_dir="/home/<USER>/common_tools/type_test_src_v_3_1/${base_dir}/${key}"
    mkdir -p "$output_dir"
    upload_dir="/home/<USER>/common_tools/type_test_v_3_1_upload/${base_dir}/${key}"
    mkdir -p "$upload_dir"

    # 创建新的shell环境执行编译，避免环境变量污染
    (
        # 执行环境设置命令
        eval "${gcc_map[$key]}"

        # 编译
        make all
        if [ $? -eq 0 ]; then
            # 移动编译结果到对应目录
            cp can/can_performance/can_type $output_dir/target/item
            cp i2c/i2c_test/i2c_type $output_dir/target/item
            cp spi/spi_test/spi_type $output_dir/target/item
            cp stress_test/test_start $output_dir/target
            cp tty/tty_performance/tty_type $output_dir/target/item
            cp wd/wd_test/wd_type $output_dir/target/item
            cp nand_check/check_nand $output_dir/target/item
            cp one_arrange.sh $output_dir
            cp one_uarrange.sh $output_dir
            cp install.sh $output_dir
            cp target/test_restart.sh $output_dir/target

            rm $output_dir/target/stress_test_A.conf
            rm $output_dir/target/stress_test_B.conf

            cp $output_dir/A/stress_test_A.conf $output_dir/target/stress_test_A.conf
            tar -cvf "$output_dir/target.tar" -C "$output_dir/target" .
            cat $output_dir/install.sh $output_dir/target.tar > $output_dir/type_test${key}_A_31_01.bin
            cp $output_dir/type_test${key}_A_31_01.bin $upload_dir/type_test${key}_A_31_01.bin

            rm $output_dir/target/stress_test_A.conf
            cp $output_dir/B/stress_test_B.conf $output_dir/target/stress_test_B.conf
            rm $output_dir/target.tar
            tar -cvf "$output_dir/target.tar" -C "$output_dir/target" .
            cat $output_dir/install.sh $output_dir/target.tar > $output_dir/type_test${key}_B_31_01.bin
            cp $output_dir/type_test${key}_B_31_01.bin $upload_dir/type_test${key}_B_31_01.bin

            message="✓ $key 打包成功，已移动到 $output_dir/"
            echo "$message"
            echo "$message" >> "$LOG_FILE"
        else
            message="✗ $key 版本编译失败"
            echo "$message"
            echo "$message" >> "$LOG_FILE"
        fi

        # 清理编译文件
        make clean
    )
    echo "----------------------------------------"
done

echo "全部编译完成！"

# # 按照原始顺序输出结果
# for key in "${key_order[@]}"; do
#     echo "(Key): $key"
#     echo "(Value):"
#     echo "${gcc_map[$key]}"
#     echo "----------------------------------------"
# done
